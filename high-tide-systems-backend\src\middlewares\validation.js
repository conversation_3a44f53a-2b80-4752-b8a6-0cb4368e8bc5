// src/middlewares/validation.js
const validationService = require('../services/validationService');
const loggerService = require('../services/loggerService');

/**
 * Middleware factory para validação usando o ValidationService
 * @param {string} schemaPath - Camin<PERSON> do schema (ex: 'user.create', 'person.update')
 * @param {object} options - Opções de validação
 * @returns {Function} Middleware Express
 */
function validateRequest(schemaPath, options = {}) {
  return async (req, res, next) => {
    const requestLogger = req.logger || loggerService.child({
      middleware: 'validation',
      schemaPath,
      method: req.method,
      url: req.originalUrl
    });

    try {
      // Determinar qual dados validar
      let dataToValidate;
      if (options.validateQuery) {
        dataToValidate = req.query;
      } else if (options.validateParams) {
        dataToValidate = req.params;
      } else {
        dataToValidate = req.body;
      }

      requestLogger.debug('VALIDATION_START', {
        schemaPath,
        dataKeys: Object.keys(dataToValidate),
        options
      });

      // Executar validação
      const result = await validationService.validate(dataToValidate, schemaPath, options);

      if (!result.isValid) {
        requestLogger.warn('VALIDATION_FAILED', {
          schemaPath,
          errors: result.errors,
          originalData: dataToValidate
        });

        return res.status(400).json({
          message: 'Dados inválidos',
          errors: result.errors,
          code: 'VALIDATION_ERROR'
        });
      }

      // Substituir dados originais pelos dados validados e sanitizados
      if (options.validateQuery) {
        req.query = result.data;
        req.validatedQuery = result.data;
      } else if (options.validateParams) {
        req.params = result.data;
        req.validatedParams = result.data;
      } else {
        req.body = result.data;
        req.validatedBody = result.data;
      }

      requestLogger.debug('VALIDATION_SUCCESS', {
        schemaPath,
        validatedKeys: Object.keys(result.data)
      });

      next();
    } catch (error) {
      requestLogger.error('VALIDATION_MIDDLEWARE_ERROR', {
        error: error.message,
        stack: error.stack,
        schemaPath
      });

      res.status(500).json({
        message: 'Erro interno de validação',
        code: 'VALIDATION_SERVICE_ERROR'
      });
    }
  };
}

/**
 * Middleware para validar parâmetros de paginação
 */
function validatePagination(req, res, next) {
  return validateRequest('pagination', { validateQuery: true })(req, res, next);
}

/**
 * Middleware para validar UUID em parâmetros
 */
function validateUuidParam(paramName = 'id') {
  return (req, res, next) => {
    const requestLogger = req.logger || loggerService.child({
      middleware: 'validateUuidParam',
      paramName
    });

    const paramValue = req.params[paramName];
    
    if (!paramValue) {
      requestLogger.warn('UUID_PARAM_MISSING', { paramName });
      return res.status(400).json({
        message: `Parâmetro ${paramName} é obrigatório`,
        code: 'MISSING_PARAM'
      });
    }

    // Regex para validar UUID v4
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    
    if (!uuidRegex.test(paramValue)) {
      requestLogger.warn('UUID_PARAM_INVALID', { paramName, paramValue });
      return res.status(400).json({
        message: `Parâmetro ${paramName} deve ser um UUID válido`,
        code: 'INVALID_UUID'
      });
    }

    requestLogger.debug('UUID_PARAM_VALID', { paramName, paramValue });
    next();
  };
}

/**
 * Middleware para validar dados de criação de usuário
 */
function validateUserCreate(req, res, next) {
  return validateRequest('user.create')(req, res, next);
}

/**
 * Middleware para validar dados de atualização de usuário
 */
function validateUserUpdate(req, res, next) {
  return validateRequest('user.update')(req, res, next);
}

/**
 * Middleware para validar dados de criação de pessoa
 */
function validatePersonCreate(req, res, next) {
  return validateRequest('person.create')(req, res, next);
}

/**
 * Middleware para validar dados de atualização de pessoa
 */
function validatePersonUpdate(req, res, next) {
  return validateRequest('person.update')(req, res, next);
}

/**
 * Middleware para validar dados de criação de empresa
 */
function validateCompanyCreate(req, res, next) {
  return validateRequest('company.create')(req, res, next);
}

/**
 * Middleware para validar dados de atualização de empresa
 */
function validateCompanyUpdate(req, res, next) {
  return validateRequest('company.update')(req, res, next);
}

/**
 * Middleware para validar dados de criação de agendamento
 */
function validateSchedulingCreate(req, res, next) {
  return validateRequest('scheduling.create')(req, res, next);
}

/**
 * Middleware para validar dados de atualização de agendamento
 */
function validateSchedulingUpdate(req, res, next) {
  return validateRequest('scheduling.update')(req, res, next);
}

/**
 * Middleware para validar dados de login
 */
function validateLogin(req, res, next) {
  return validateRequest('auth.login')(req, res, next);
}

/**
 * Middleware para validar dados de mudança de senha
 */
function validateChangePassword(req, res, next) {
  return validateRequest('auth.changePassword')(req, res, next);
}

/**
 * Middleware para validar dados de reset de senha
 */
function validateResetPassword(req, res, next) {
  return validateRequest('auth.resetPassword')(req, res, next);
}

/**
 * Middleware para sanitizar dados de entrada
 * Remove campos não permitidos e aplica sanitização básica
 */
function sanitizeInput(allowedFields = []) {
  return (req, res, next) => {
    const requestLogger = req.logger || loggerService.child({
      middleware: 'sanitizeInput'
    });

    if (allowedFields.length > 0 && req.body) {
      const originalKeys = Object.keys(req.body);
      const sanitizedBody = {};

      allowedFields.forEach(field => {
        if (req.body.hasOwnProperty(field)) {
          sanitizedBody[field] = req.body[field];
        }
      });

      const removedKeys = originalKeys.filter(key => !allowedFields.includes(key));
      
      if (removedKeys.length > 0) {
        requestLogger.warn('FIELDS_REMOVED_BY_SANITIZATION', {
          removedFields: removedKeys,
          allowedFields
        });
      }

      req.body = sanitizedBody;
    }

    next();
  };
}

module.exports = {
  validateRequest,
  validatePagination,
  validateUuidParam,
  validateUserCreate,
  validateUserUpdate,
  validatePersonCreate,
  validatePersonUpdate,
  validateCompanyCreate,
  validateCompanyUpdate,
  validateSchedulingCreate,
  validateSchedulingUpdate,
  validateLogin,
  validateChangePassword,
  validateResetPassword,
  sanitizeInput
};
