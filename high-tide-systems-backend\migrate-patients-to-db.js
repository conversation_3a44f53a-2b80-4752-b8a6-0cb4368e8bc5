/*
 * SCRIPT DE CORREÇÃO DA MIGRAÇÃO DE PACIENTES
 * 
 * Este script corrige problemas na estrutura de dados após a migração:
 * 
 * 1. ATUALIZA relacionamentos antigos:
 *    'mae', 'pai', 'responsavel' → 'dependente' 
 *    'self' → 'titular'
 * 
 * 2. CRIA Persons para responsáveis que só tinham Client (login) mas não Person (dados)
 * 
 * 3. CRIA Clients para pais/mães que foram ignorados na migração inicial
 * 
 * 4. CORRIGE dados de gênero trocados no CSV original (detectados automaticamente)
 * 
 * IMPORTANTE: O CSV contém apenas PACIENTES (142 registros).
 * Responsáveis que não aparecem no CSV foram criados durante a migração e isso é NORMAL.
 */

const fs = require('fs');
const Papa = require('papaparse');
const bcrypt = require('bcryptjs');
const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

const prisma = new PrismaClient();

// CONFIGURAÇÕES
const DEFAULT_PASSWORD = process.env.DEFAULT_PASSWORD || 'TrocarSenha123!';

// Função para detectar gênero por nome
function detectarGenero(nome) {
  if (!nome) return null;
  
  const nomeMinusculo = nome.toLowerCase();
  
  const nomesMasculinos = [
    'joão', 'jose', 'antonio', 'francisco', 'carlos', 'paulo', 'pedro', 'lucas', 'luiz', 'marcos',
    'luis', 'gabriel', 'rafael', 'daniel', 'marcelo', 'bruno', 'eduardo', 'felipe', 'raimundo',
    'rodrigo', 'manoel', 'nelson', 'roberto', 'fabio', 'leonardo', 'andre', 'ricardo', 'wagner',
    'sebastiao', 'joao', 'fernando', 'fabiano', 'caio', 'diego', 'gustavo', 'henrique', 'igor',
    'jefferson', 'julio', 'edgar', 'robson', 'leirson', 'manoel', 'jair', 'fernando'
  ];
  
  const nomesFemininos = [
    'maria', 'ana', 'francisca', 'antonia', 'adriana', 'juliana', 'marcia', 'fernanda', 'patricia',
    'aline', 'sandra', 'camila', 'amanda', 'bruna', 'jessica', 'leticia', 'julia', 'mariana',
    'carolina', 'isabela', 'larissa', 'gabriela', 'rafaela', 'carla', 'monica', 'andrea', 'luciana',
    'cristiane', 'daniela', 'claudia', 'simone', 'viviane', 'barbara', 'beatriz', 'renata', 'kelly',
    'grace', 'stefania', 'delvair', 'teresa', 'alessandra', 'marcely', 'tatiana', 'waleska'
  ];
  
  for (const nomeMasc of nomesMasculinos) {
    if (nomeMinusculo.includes(nomeMasc)) {
      return 'M';
    }
  }
  
  for (const nomeFem of nomesFemininos) {
    if (nomeMinusculo.includes(nomeFem)) {
      return 'F';
    }
  }
  
  return null;
}

// Função para ler CSV
async function readCSV(filePath) {
  try {
    console.log(`📖 Lendo arquivo: ${filePath}`);
    const fileContent = fs.readFileSync(filePath, 'utf8');
    
    if (!fileContent || fileContent.trim().length === 0) {
      throw new Error(`Arquivo ${filePath} está vazio`);
    }
    
    console.log(`📏 Tamanho do arquivo: ${fileContent.length} caracteres`);
    console.log(`📝 Primeiras linhas do arquivo:`);
    console.log(fileContent.split('\n').slice(0, 3).join('\n'));
    
    const results = Papa.parse(fileContent, {
      header: true,
      skipEmptyLines: true,
      dynamicTyping: false,
      transformHeader: (header) => header.trim(),
      transform: (value, header) => {
        if (value === '' || value === 'null' || value === 'undefined') {
          return null;
        }
        if (['ativo', 'active'].includes(header.toLowerCase())) {
          return value === 'true' || value === true;
        }
        return value;
      }
    });
    
    console.log(`📊 Parsing concluído: ${results.data.length} registros`);
    if (results.errors && results.errors.length > 0) {
      console.warn('⚠️ Erros durante o parsing:', results.errors.slice(0, 5));
    }
    
    return results.data;
  } catch (error) {
    console.error(`❌ Erro ao ler arquivo ${filePath}:`, error.message);
    throw error;
  }
}

// Função para encontrar empresa da migração
async function findMigrationCompany() {
  const company = await prisma.company.findFirst({
    where: {
      OR: [
        { name: 'ABA+ Migrada' },
        { name: process.env.DEFAULT_COMPANY_NAME || 'ABA+ Migrada' }
      ]
    }
  });

  if (!company) {
    throw new Error('Empresa da migração não encontrada.');
  }

  return company;
}

// Função para gerar login único
function generateLogin(nome, codigo) {
  if (!nome) return `paciente${codigo}`;

  const cleanName = nome
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '')
    .substring(0, 15);

  return cleanName || `paciente${codigo}`;
}

// Função para gerar email único
async function generateUniqueEmail(baseEmail, codigo) {
  let email = baseEmail;
  let counter = 1;
  
  while (true) {
    const existingClient = await prisma.client.findFirst({
      where: { email: email }
    });
    
    if (!existingClient) {
      return email;
    }
    
    const [prefix, domain] = baseEmail.split('@');
    email = `${prefix}${counter}@${domain}`;
    counter++;
    
    if (counter > 100) {
      email = `paciente${codigo}${Date.now()}@responsavel.com`;
      break;
    }
  }
  
  return email;
}

// Função para gerar login único
async function generateUniqueLogin(baseLogin) {
  let login = baseLogin;
  let counter = 1;
  
  while (true) {
    const existingClient = await prisma.client.findFirst({
      where: { login: login }
    });
    
    if (!existingClient) {
      return login;
    }
    
    login = `${baseLogin}${counter}`;
    counter++;
    
    if (counter > 100) {
      login = `${baseLogin}${Date.now()}`;
      break;
    }
  }
  
  return login;
}

// Função para capitalizar nome
function capitalizarNome(nome) {
  if (!nome) return '';
  
  return nome
    .toLowerCase()
    .split(' ')
    .map(palavra => {
      if (palavra.length === 0) return palavra;
      return palavra.charAt(0).toUpperCase() + palavra.slice(1);
    })
    .join(' ');
}

// Função principal de correção
async function patchMigration() {
  try {
    console.log('🔧 Iniciando CORREÇÃO dos dados de migração existentes...\n');

    const company = await findMigrationCompany();
    console.log(`✓ Empresa encontrada: ${company.name}`);

    // Carregar dados originais para referência
    if (!fs.existsSync('pacientes_dados_brutos.csv')) {
      throw new Error('Arquivo pacientes_dados_brutos.csv não encontrado');
    }

    console.log('📂 Carregando dados do CSV...');
    const pacientesData = await readCSV('pacientes_dados_brutos.csv');
    
    if (!pacientesData || pacientesData.length === 0) {
      throw new Error('Nenhum dado foi carregado do CSV ou arquivo está vazio');
    }
    
    console.log(`✓ ${pacientesData.length} pacientes carregados dos dados originais`);

    // Verificar estrutura do CSV primeiro
    console.log('📋 Analisando CSV de pacientes...');
    if (pacientesData.length > 0) {
      console.log('Colunas disponíveis:', Object.keys(pacientesData[0]));
      console.log('Amostra do primeiro registro:', {
        codigo: pacientesData[0].codigo,
        nome: pacientesData[0].nome,
        mae: pacientesData[0].mae,
        pai: pacientesData[0].pai
      });
    }

    // Analisar problemas de gênero nos dados do CSV
    let problemasTrocados = 0;
    pacientesData.forEach(paciente => {
      const mae = paciente.mae?.toString().trim();
      const pai = paciente.pai?.toString().trim();
      const temMae = mae && mae !== 'null' && mae !== '';
      const temPai = pai && pai !== 'null' && pai !== '';
      
      if (temMae && temPai) {
        const generoMae = detectarGenero(mae);
        const generoPai = detectarGenero(pai);
        
        if (generoMae === 'M' && generoPai === 'F') {
          problemasTrocados++;
        }
      }
    });
    
    console.log(`📊 Análise do CSV: ${pacientesData.length} pacientes, ${problemasTrocados} com gêneros trocados`);

    // Variáveis para estatísticas finais
    let pacientesEncontrados = 0;
    let responsaveisEncontrados = 0;
    let pacientesNaoEncontrados = [];
    let responsaveisNaoEncontrados = [];

    // Função para normalizar nomes para comparação
    function normalizarNome(nome) {
      if (!nome) return '';
      return nome.toString()
        .trim()
        .toLowerCase()
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '') // Remove acentos
        .replace(/\s+/g, ' '); // Normaliza espaços
    }

    // Criar mapa dos dados originais por nome completo normalizado
    const pacientesMap = new Map();
    const pacientesMapAlternativo = new Map(); // Backup com várias chaves possíveis
    
    pacientesData.forEach((paciente, index) => {
      // Tentar diferentes campos que podem conter o nome
      const possiveisNomes = [
        paciente.nome,
        paciente.name,
        paciente.fullName,
        paciente.nomeCompleto,
        paciente.nomePaciente
      ];
      
      for (const nomeField of possiveisNomes) {
        if (nomeField) {
          const nome = nomeField.toString().trim();
          const nomeNormalizado = normalizarNome(nome);
          
          if (nome && nomeNormalizado) {
            // Mapa principal com nome original
            pacientesMap.set(nome, paciente);
            // Mapa alternativo com nome normalizado
            pacientesMapAlternativo.set(nomeNormalizado, paciente);
            
            // Também mapear por código se existir
            if (paciente.codigo) {
              pacientesMapAlternativo.set(paciente.codigo.toString(), paciente);
            }
            break; // Para no primeiro nome válido encontrado
          }
        }
      }
      
      // Debug para registros problemáticos
      if (index < 5) {
        console.log(`Registro ${index + 1}:`, {
          possiveisNomes: possiveisNomes.filter(n => n),
          codigo: paciente.codigo
        });
      }
    });

    console.log(`✓ ${pacientesMap.size} pacientes mapeados por nome original`);
    console.log(`✓ ${pacientesMapAlternativo.size} entradas no mapa alternativo`);

    // Função melhorada para buscar paciente original
    function buscarPacienteOriginal(nomeCompleto, ehResponsavel = false) {
      // Tentar busca direta primeiro
      let paciente = pacientesMap.get(nomeCompleto);
      if (paciente) return paciente;
      
      // Tentar busca normalizada
      const nomeNormalizado = normalizarNome(nomeCompleto);
      paciente = pacientesMapAlternativo.get(nomeNormalizado);
      if (paciente) return paciente;
      
      // Tentar busca por similaridade (primeiras palavras)
      const palavrasNome = nomeNormalizado.split(' ').filter(p => p.length > 2);
      if (palavrasNome.length >= 2) {
        const nomeReduzido = palavrasNome.slice(0, 2).join(' ');
        
        for (const [key, value] of pacientesMapAlternativo.entries()) {
          if (key.includes(nomeReduzido) || nomeReduzido.includes(key)) {
            console.log(`  🔍 Encontrado por similaridade: "${nomeCompleto}" → "${key}"`);
            return value;
          }
        }
      }
      
      // Se é responsável e não encontrou, isso é normal
      if (ehResponsavel) {
        return null; // Responsáveis podem não estar no CSV de pacientes
      }
      
      return null;
    }

    // Função para detectar se é nome de responsável (heurística)
    function pareceResponsavel(nomeCompleto) {
      // Responsáveis geralmente têm nomes de adultos mais comuns
      const primeiroNome = nomeCompleto.split(' ')[0].toLowerCase();
      const nomesAdultosComuns = [
        'maria', 'jose', 'antonio', 'francisco', 'ana', 'carlos', 'paulo', 'pedro',
        'lucas', 'luiz', 'marcos', 'gabriel', 'rafael', 'daniel', 'marcelo', 'bruno',
        'eduardo', 'felipe', 'rodrigo', 'manoel', 'nelson', 'roberto', 'fabio'
      ];
      
      return nomesAdultosComuns.includes(primeiroNome);
    }

    const defaultCreatorId = (await prisma.user.findFirst({
      where: { companyId: company.id, role: 'COMPANY_ADMIN' }
    }))?.id;

    const saltRounds = parseInt(process.env.BCRYPT_SALT_ROUNDS) || 12;
    const hashedPassword = await bcrypt.hash(DEFAULT_PASSWORD, saltRounds);

    // Contadores
    let relationshipsUpdated = 0;
    let clientsCreated = 0;
    let personsCreated = 0;
    let clientPersonsCreated = 0;
    let problemasCorrigidos = 0;

    console.log('\n📝 ETAPA 1: Atualizando relacionamentos existentes...');
    
    // 1. Atualizar relacionamentos antigos para os novos padrões
    const oldRelationships = await prisma.clientPerson.findMany({
      where: {
        client: { companyId: company.id },
        relationship: { in: ['mae', 'pai', 'self', 'responsavel'] }
      },
      include: {
        client: true,
        person: true
      }
    });

    for (const cp of oldRelationships) {
      let newRelationship;
      
      // Se é 'self', vira 'titular' (próprio paciente)
      if (cp.relationship === 'self') {
        newRelationship = 'titular';
      } else {
        // Se é 'mae', 'pai' ou 'responsavel', vira 'dependente' (responsável por criança)
        newRelationship = 'dependente';
      }
      
      await prisma.clientPerson.update({
        where: { id: cp.id },
        data: { relationship: newRelationship }
      });
      relationshipsUpdated++;
      console.log(`  ✓ ${cp.person.fullName}: "${cp.relationship}" → "${newRelationship}"`);
    }

    console.log(`\n✓ ${relationshipsUpdated} relacionamentos atualizados`);

    console.log('\n👤 ETAPA 2: Criando Persons para responsáveis...');

    // 2. Buscar Clients que só têm relacionamento com crianças mas não têm Person própria
    const clientsWithoutOwnPerson = await prisma.client.findMany({
      where: {
        companyId: company.id,
        clientPersons: {
          // Tem relacionamento com crianças
          some: {
            relationship: 'dependente'
          },
          // Mas não tem relacionamento 'titular' (própria Person)
          none: {
            relationship: 'titular'
          }
        }
      },
      include: {
        clientPersons: {
          include: {
            person: true
          }
        }
      }
    });

    console.log(`  📊 Encontrados ${clientsWithoutOwnPerson.length} responsáveis sem Person própria`);

    for (const client of clientsWithoutOwnPerson) {
      try {
        // Buscar dados originais para obter o nome real da mãe/pai
        const criancaRelacionada = client.clientPersons.find(cp => cp.relationship === 'dependente')?.person;
        const pacienteOriginal = criancaRelacionada ? buscarPacienteOriginal(criancaRelacionada.fullName, false) : null;
        
        let nomeResponsavel = client.login; // fallback
        
        if (pacienteOriginal) {
          // Usar nome da mãe ou pai dos dados originais
          const mae = pacienteOriginal.mae?.toString().trim();
          const pai = pacienteOriginal.pai?.toString().trim();
          const temMae = mae && mae !== 'null' && mae !== '';
          const temPai = pai && pai !== 'null' && pai !== '';
          
          // Verificar se client corresponde à mãe ou pai pelo email/login
          if (temMae && client.email.toLowerCase().includes(generateLogin(mae, '').toLowerCase())) {
            nomeResponsavel = mae;
          } else if (temPai && client.email.toLowerCase().includes(generateLogin(pai, '').toLowerCase())) {
            nomeResponsavel = pai;
          } else if (temMae) {
            // Se não conseguir identificar, usar mãe como padrão
            nomeResponsavel = mae;
          } else if (temPai) {
            nomeResponsavel = pai;
          }
        }
        
        // Se ainda não tem nome, tentar extrair do email
        if (nomeResponsavel === client.login && client.email && client.email.includes('@')) {
          const emailPrefix = client.email.split('@')[0];
          if (!/\d/.test(emailPrefix)) {
            nomeResponsavel = emailPrefix;
          }
        }

        // Capitalizar nome corretamente (Title Case)
        nomeResponsavel = capitalizarNome(nomeResponsavel);

        // Buscar dados da primeira criança para usar endereço/telefone
        const dadosCrianca = client.clientPersons.find(cp => cp.relationship === 'dependente')?.person;

        // Criar Person para o responsável
        const personResponsavel = await prisma.person.create({
          data: {
            email: client.email,
            phone: dadosCrianca?.phone || null,
            address: dadosCrianca?.address || null,
            city: dadosCrianca?.city || null,
            state: dadosCrianca?.state || null,
            birthDate: null, // Não temos data nascimento do responsável
            notes: `Responsável criado automaticamente`,
            active: client.active,
            createdAt: client.createdAt,
            updatedAt: new Date(),
            neighborhood: dadosCrianca?.neighborhood || null,
            postalCode: dadosCrianca?.postalCode || null,
            cpf: null, // Não temos CPF do responsável
            createdById: defaultCreatorId,
            fullName: nomeResponsavel,
            gender: null, // Não conseguimos detectar do login
            profileImageUrl: null,
            relationship: null,
            useClientEmail: true,
            useClientPhone: true
          }
        });

        personsCreated++;
        console.log(`    ✓ Person criada para responsável: ${nomeResponsavel}`);

        // Criar relação ClientPerson: responsável → própria Person
        await prisma.clientPerson.create({
          data: {
            clientId: client.id,
            personId: personResponsavel.id,
            relationship: 'titular',
            isPrimary: true
          }
        });

        clientPersonsCreated++;
        console.log(`    ✓ Relação titular criada para: ${nomeResponsavel}`);

      } catch (error) {
        console.error(`    ❌ Erro ao criar Person para ${client.login}:`, error.message);
      }
    }

    console.log('\n👥 ETAPA 3: Criando Clients para pais que foram ignorados...');

    // 3. Buscar pessoas que só têm um responsável mas deveriam ter dois
    const personsWithClients = await prisma.person.findMany({
      where: {
        clientPersons: {
          some: {
            client: { companyId: company.id }
          }
        }
      },
      include: {
        clientPersons: {
          include: {
            client: true
          },
          where: {
            client: { companyId: company.id }
          }
        }
      }
    });

    for (const person of personsWithClients) {
      try {
        const ehPaciente = !pareceResponsavel(person.fullName);
        const pacienteOriginal = buscarPacienteOriginal(person.fullName, !ehPaciente);
        
        if (!pacienteOriginal) {
          if (ehPaciente) {
            console.log(`  ⚠️  Paciente não encontrado no CSV: ${person.fullName}`);
          } else {
            console.log(`  ℹ️  Responsável não encontrado no CSV (normal): ${person.fullName}`);
          }
          continue;
        }

        const mae = pacienteOriginal.mae?.toString().trim();
        const pai = pacienteOriginal.pai?.toString().trim();
        const temMae = mae && mae !== 'null' && mae !== '';
        const temPai = pai && pai !== 'null' && pai !== '';
        
        // Verificar se tem dados de gênero trocados
        let maeCorrigida = mae;
        let paiCorrigido = pai;
        
        if (temMae && temPai) {
          const generoMae = detectarGenero(mae);
          const generoPai = detectarGenero(pai);
          
          if (generoMae === 'M' && generoPai === 'F') {
            maeCorrigida = pai;
            paiCorrigido = mae;
            console.log(`  🔄 Corrigindo gêneros trocados para ${person.fullName}: ${mae} ↔ ${pai}`);
            problemasCorrigidos++;
          }
        }

        const clientsExistentes = person.clientPersons.length;
        
        // Se deveria ter 2 responsáveis mas só tem 1
        if ((temMae && temPai) && clientsExistentes === 1) {
          console.log(`\n  👤 Processando: ${person.fullName}`);
          console.log(`    Tem mãe: ${maeCorrigida || 'Não'}`);
          console.log(`    Tem pai: ${paiCorrigido || 'Não'}`);
          console.log(`    Clients existentes: ${clientsExistentes}`);

          // Verificar qual responsável já existe
          const clientExistente = person.clientPersons[0].client;
          const emailExistente = clientExistente.email;
          
          // Tentar identificar se o client existente é da mãe ou pai
          let clientExistenteEhMae = false;
          
          // Verificar pelo email se contém nome da mãe ou pai
          if (emailExistente.toLowerCase().includes(generateLogin(maeCorrigida || '', '').toLowerCase())) {
            clientExistenteEhMae = true;
          } else if (emailExistente.toLowerCase().includes(generateLogin(paiCorrigido || '', '').toLowerCase())) {
            clientExistenteEhMae = false;
          } else {
            // Usar heurística: se só tem usuário.nome, é provável que seja da mãe (padrão antigo)
            clientExistenteEhMae = true;
          }

          // Criar client para o responsável que está faltando
          let nomeResponsavelFaltando = clientExistenteEhMae ? paiCorrigido : maeCorrigida;
          const loginFaltando = generateLogin(nomeResponsavelFaltando, pacienteOriginal.codigo);
          const emailFaltando = `${loginFaltando}@responsavel.com`;

          // Capitalizar nome do responsável faltando corretamente
          nomeResponsavelFaltando = capitalizarNome(nomeResponsavelFaltando);
          
          const uniqueLogin = await generateUniqueLogin(loginFaltando);
          const uniqueEmail = await generateUniqueEmail(emailFaltando, pacienteOriginal.codigo);

          const novoClient = await prisma.client.create({
            data: {
              login: uniqueLogin,
              email: uniqueEmail,
              password: hashedPassword,
              active: pacienteOriginal.ativo !== false,
              createdAt: new Date(),
              updatedAt: new Date(),
              createdById: defaultCreatorId,
              companyId: company.id
            }
          });

          // Criar Person para o novo responsável
          const generoResponsavel = clientExistenteEhMae ? 'M' : 'F'; // Pai = M, Mãe = F
          
          const personNovoResponsavel = await prisma.person.create({
            data: {
              email: uniqueEmail,
              phone: person.phone,
              address: person.address,
              city: person.city,
              state: person.state,
              birthDate: null,
              notes: `Responsável (${clientExistenteEhMae ? 'pai' : 'mãe'}) de ${person.fullName}`,
              active: person.active,
              createdAt: new Date(),
              updatedAt: new Date(),
              neighborhood: person.neighborhood,
              postalCode: person.postalCode,
              cpf: null,
              createdById: defaultCreatorId,
              fullName: nomeResponsavelFaltando,
              gender: generoResponsavel,
              profileImageUrl: null,
              relationship: null,
              useClientEmail: true,
              useClientPhone: true
            }
          });

          personsCreated++;
          console.log(`    ✓ Person criada para responsável: ${nomeResponsavelFaltando}`);

          // Criar relação ClientPerson: responsável → própria Person
          await prisma.clientPerson.create({
            data: {
              clientId: novoClient.id,
              personId: personNovoResponsavel.id,
              relationship: 'titular',
              isPrimary: true
            }
          });

          clientPersonsCreated++;
          console.log(`    ✓ Relação titular criada para: ${nomeResponsavelFaltando}`);

          // Criar relação ClientPerson: responsável → criança
          await prisma.clientPerson.create({
            data: {
              clientId: novoClient.id,
              personId: person.id,
              relationship: 'dependente',
              isPrimary: false // O existente continua sendo primário
            }
          });

          clientsCreated++;
          clientPersonsCreated++;
          
          console.log(`    ✓ Client criado para ${clientExistenteEhMae ? 'pai' : 'mãe'}: ${nomeResponsavelFaltando} (${uniqueEmail})`);
        }

        // Garantir que isPrimary está correto
        if (person.clientPersons.length > 0) {
          const primaryExists = person.clientPersons.some(cp => cp.isPrimary);
          
          if (!primaryExists) {
            // Marcar o primeiro como primário
            await prisma.clientPerson.update({
              where: { id: person.clientPersons[0].id },
              data: { isPrimary: true }
            });
            console.log(`    ✓ Marcado como primário: ${person.clientPersons[0].client.login}`);
          }
        }
      } catch (error) {
        console.error(`    ❌ Erro ao processar pessoa ${person.fullName}:`, error.message);
      }
    }

    console.log('\n🔍 ETAPA 4: Verificação final...');

    // Mostrar estatísticas de mapeamento
    const pessoasTotal = await prisma.person.count({
      where: {
        clientPersons: {
          some: {
            client: { companyId: company.id }
          }
        }
      }
    });
    
    // Reset das variáveis para contagem final
    pacientesEncontrados = 0;
    responsaveisEncontrados = 0;
    pacientesNaoEncontrados = [];
    responsaveisNaoEncontrados = [];
    
    const todasPessoas = await prisma.person.findMany({
      where: {
        clientPersons: {
          some: {
            client: { companyId: company.id }
          }
        }
      },
      select: { fullName: true }
    });
    
    todasPessoas.forEach(pessoa => {
      const ehPaciente = !pareceResponsavel(pessoa.fullName);
      const encontrado = buscarPacienteOriginal(pessoa.fullName, !ehPaciente);
      
      if (encontrado) {
        if (ehPaciente) {
          pacientesEncontrados++;
        } else {
          responsaveisEncontrados++;
        }
      } else {
        if (ehPaciente) {
          pacientesNaoEncontrados.push(pessoa.fullName);
        } else {
          responsaveisNaoEncontrados.push(pessoa.fullName);
        }
      }
    });
    
    console.log(`\n📋 Estatísticas de mapeamento:`);
    console.log(`  Total de pessoas no banco: ${pessoasTotal}`);
    console.log(`  📊 PACIENTES:`);
    console.log(`    - Encontrados no CSV: ${pacientesEncontrados}`);
    console.log(`    - Não encontrados: ${pacientesNaoEncontrados.length}`);
    console.log(`  👥 RESPONSÁVEIS:`);
    console.log(`    - Encontrados no CSV: ${responsaveisEncontrados}`);
    console.log(`    - Não encontrados: ${responsaveisNaoEncontrados.length} (normal, são responsáveis criados)`);
    
    if (pacientesNaoEncontrados.length > 0) {
      console.log(`\n⚠️ Pacientes não encontrados no CSV:`, pacientesNaoEncontrados.slice(0, 5));
      if (pacientesNaoEncontrados.length > 5) {
        console.log(`... e mais ${pacientesNaoEncontrados.length - 5}`);
      }
    }

    // 4. Verificar resultado final
    const finalStats = await prisma.clientPerson.groupBy({
      by: ['relationship'],
      where: {
        client: { companyId: company.id }
      },
      _count: true
    });

    console.log('\n📊 Estatísticas finais dos relacionamentos:');
    finalStats.forEach(stat => {
      console.log(`  ${stat.relationship}: ${stat._count} relacionamentos`);
    });

    // Verificar pessoas com múltiplos responsáveis
    const pessoasComMultiplosResp = await prisma.person.findMany({
      where: {
        clientPersons: {
          some: {
            client: { companyId: company.id }
          }
        }
      },
      include: {
        clientPersons: {
          where: {
            client: { companyId: company.id }
          },
          include: { client: true }
        }
      }
    });

    const comMultiplosResp = pessoasComMultiplosResp.filter(p => p.clientPersons.length > 1);
    console.log(`\n👨‍👩‍👧‍👦 Pessoas com múltiplos responsáveis: ${comMultiplosResp.length}`);

    // Gerar relatório
    const report = {
      timestamp: new Date().toISOString(),
      type: "PATCH_MIGRATION",
      company: { id: company.id, name: company.name },
      csvAnalysis: {
        totalPacientesCSV: pacientesData.length,
        problemasTrocados,
        registrosMapeados: pacientesMap.size
      },
      statistics: {
        relationshipsUpdated,
        clientsCreated,
        personsCreated,
        clientPersonsCreated,
        problemasGeneroCorrigidos: problemasCorrigidos,
        pessoasComMultiplosResponsaveis: comMultiplosResp.length,
        pacientesEncontrados,
        responsaveisEncontrados,
        pacientesNaoEncontrados: pacientesNaoEncontrados.length,
        responsaveisNaoEncontrados: responsaveisNaoEncontrados.length
      },
      corrections: [
        `✅ ${relationshipsUpdated} relacionamentos atualizados ('mae'/'pai'→'dependente', 'self'→'titular')`,
        `✅ ${personsCreated} Persons criadas para responsáveis que não tinham dados próprios`,
        `✅ ${clientsCreated} Clients criados para pais que estavam faltando`,
        `✅ ${clientPersonsCreated} relacionamentos Client-Person criados`,
        `✅ ${problemasCorrigidos} problemas de gênero detectados e corrigidos`,
        `✅ Estrutura de dados normalizada para o frontend`,
        `✅ ${pacientesEncontrados} pacientes mapeados corretamente`,
        `✅ ${responsaveisNaoEncontrados} responsáveis não encontrados no CSV (normal)`
      ],
      warnings: [
        'Dados existentes foram preservados e corrigidos',
        'Novos Clients criados com senha padrão: ' + DEFAULT_PASSWORD,
        'Responsáveis não encontrados no CSV é situação normal',
        'CSV contém apenas pacientes, não todos os responsáveis',
        'Verificar se todos os relacionamentos estão corretos'
      ],
      problematicRecords: pacientesNaoEncontrados.length > 0 ? {
        pacientesNaoEncontrados: pacientesNaoEncontrados.slice(0, 10),
        total: pacientesNaoEncontrados.length
      } : null
    };

    fs.writeFileSync('relatorio_patch_migracao.json', JSON.stringify(report, null, 2));

    console.log('\n🎉 CORREÇÃO concluída com sucesso!');
    console.log('\n📊 Resumo das correções:');
    console.log(`   • Relacionamentos atualizados: ${relationshipsUpdated}`);
    console.log(`   • Persons criadas para responsáveis: ${personsCreated}`);
    console.log(`   • Novos Clients criados: ${clientsCreated}`);
    console.log(`   • Problemas de gênero corrigidos: ${problemasCorrigidos}`);
    console.log(`   • Pessoas com múltiplos responsáveis: ${comMultiplosResp.length}`);

    console.log('\n✅ SITUAÇÃO ATUAL:');
    console.log('   • Cada responsável tem: Client (login) + Person (dados) + relacionamentos corretos');
    console.log('   • Relacionamentos padronizados: "titular" e "dependente"');
    console.log('   • Dados de gênero corrigidos automaticamente');
    console.log('   • Frontend funcionará perfeitamente!');
    
    console.log('\n📋 INFORMAÇÕES IMPORTANTES:');
    console.log(`   • Senha padrão para novos Clients: ${DEFAULT_PASSWORD}`);
    console.log('   • Avisos sobre "responsáveis não encontrados no CSV" são NORMAIS');
    console.log('   • O CSV contém apenas pacientes, não todos os responsáveis');
    console.log(`   • Relatório detalhado salvo em: relatorio_patch_migracao.json`);

  } catch (error) {
    console.error('❌ Erro durante a correção:', error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Função para verificar status atual
async function checkCurrentStatus() {
  try {
    console.log('🔍 Verificando status atual dos dados...\n');

    const company = await findMigrationCompany();
    
    // Verificar relacionamentos atuais
    const relationships = await prisma.clientPerson.groupBy({
      by: ['relationship'],
      where: {
        client: { companyId: company.id }
      },
      _count: true
    });

    console.log('📊 Relacionamentos atuais:');
    relationships.forEach(rel => {
      console.log(`  ${rel.relationship}: ${rel._count}`);
    });

    // Verificar pessoas com 1 vs múltiplos responsáveis
    const pessoas = await prisma.person.findMany({
      where: {
        clientPersons: {
          some: {
            client: { companyId: company.id }
          }
        }
      },
      include: {
        clientPersons: {
          where: {
            client: { companyId: company.id }
          }
        }
      }
    });

    const com1Resp = pessoas.filter(p => p.clientPersons.length === 1).length;
    const comMultiplosResp = pessoas.filter(p => p.clientPersons.length > 1).length;

    console.log(`\n👥 Distribuição de responsáveis:`);
    console.log(`  Pessoas com 1 responsável: ${com1Resp}`);
    console.log(`  Pessoas com múltiplos responsáveis: ${comMultiplosResp}`);

    // Verificar se há relacionamentos antigos
    const oldRelationships = await prisma.clientPerson.count({
      where: {
        client: { companyId: company.id },
        relationship: { in: ['mae', 'pai', 'self', 'responsavel'] }
      }
    });

    // Verificar responsáveis sem Person própria
    const responsaveisSemanPerson = await prisma.client.count({
      where: {
        companyId: company.id,
        clientPersons: {
          some: {
            relationship: 'dependente'
          },
          none: {
            relationship: 'titular'
          }
        }
      }
    });

    console.log(`\n🔧 Status da correção:`);
    if (oldRelationships > 0) {
      console.log(`  ❌ Ainda há ${oldRelationships} relacionamentos antigos que precisam ser corrigidos`);
    } else {
      console.log(`  ✅ Todos os relacionamentos estão no formato correto`);
    }
    
    if (responsaveisSemanPerson > 0) {
      console.log(`  ❌ Há ${responsaveisSemanPerson} responsáveis sem Person própria`);
      console.log(`  💡 Execute: node patch-migration.js`);
    } else {
      console.log(`  ✅ Todos os responsáveis têm Person própria`);
    }

  } catch (error) {
    console.error('Erro ao verificar status:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar script
if (require.main === module) {
  const command = process.argv[2];

  if (command === 'check') {
    checkCurrentStatus().catch(console.error);
  } else {
    patchMigration().catch(console.error);
  }
}

module.exports = { patchMigration, checkCurrentStatus };