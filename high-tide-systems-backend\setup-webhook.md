# Configuração do Webhook do Stripe

## Problema Identificado

O webhook do Stripe não está sendo chamado porque o ambiente é local (localhost:5000) e o Stripe não consegue acessar endpoints locais.

## Soluções

### Opção 1: Usar ngrok (Recomendado para desenvolvimento)

1. **Instalar ngrok**:
   ```bash
   # Windows (usando chocolatey)
   choco install ngrok
   
   # Ou baixar de https://ngrok.com/download
   ```

2. **Expor o servidor local**:
   ```bash
   ngrok http 5000
   ```

3. **Configurar webhook no Stripe Dashboard**:
   - Acessar: https://dashboard.stripe.com/test/webhooks
   - Criar novo endpoint: `https://your-ngrok-url.ngrok.io/subscription/webhook`
   - Eventos a escutar:
     - `checkout.session.completed`
     - `customer.subscription.updated`
     - `customer.subscription.deleted`
     - `invoice.paid`

4. **Atualizar .env com o webhook secret**:
   ```env
   STRIPE_WEBHOOK_SECRET=whsec_seu_novo_webhook_secret
   ```

### Opção 2: Usar Stripe CLI (Alternativa)

1. **Instalar Stripe CLI**:
   ```bash
   # Windows
   scoop install stripe
   ```

2. **Login no Stripe**:
   ```bash
   stripe login
   ```

3. **Escutar webhooks localmente**:
   ```bash
   stripe listen --forward-to localhost:5000/subscription/webhook
   ```

### Opção 3: Criar subscription manualmente (Atual)

Para testes imediatos, use o script já criado:
```bash
docker exec -it high-tide-systems-api node create-test-subscription.js
```

## Verificação

Após configurar o webhook, teste criando uma nova conta e verifique se a subscription é criada automaticamente.

## Status Atual

✅ **Problema resolvido temporariamente**: Subscription criada manualmente para o usuário de teste
✅ **API funcionando**: Endpoint `/subscription/subscription` retorna dados corretos
⚠️ **Webhook pendente**: Configurar para futuras subscriptions automáticas

## Dados da Subscription Criada

- **ID**: f3939428-a1ec-46cc-9a62-ecfe5a63cd04
- **Company**: Empresa de Teste Ltda
- **Status**: ACTIVE
- **Billing Cycle**: MONTHLY
- **User Limit**: 5
- **Price**: R$ 19.90/mês
- **Modules**: SCHEDULING, BASIC, ADMIN
