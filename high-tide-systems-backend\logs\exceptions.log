{"timestamp":"2025-06-15 23:01:17","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Sun Jun 15 2025 23:01:17 GMT+0000 (Coordinated Universal Time)","process":{"pid":128,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":151887872,"heapTotal":49991680,"heapUsed":46915464,"external":3008000,"arrayBuffers":144117}},"os":{"loadavg":[0.82,1.43,1.54],"uptime":31457.99},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":529,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:15:09","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Sun Jun 15 2025 23:15:09 GMT+0000 (Coordinated Universal Time)","process":{"pid":390,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":198324224,"heapTotal":111595520,"heapUsed":45602152,"external":2980538,"arrayBuffers":119281}},"os":{"loadavg":[2.65,1.91,1.72],"uptime":32289.87},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":529,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:19:39","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Sun Jun 15 2025 23:19:39 GMT+0000 (Coordinated Universal Time)","process":{"pid":542,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":200273920,"heapTotal":112381952,"heapUsed":45588576,"external":2980538,"arrayBuffers":119281}},"os":{"loadavg":[2.14,1.86,1.74],"uptime":32559.88},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":529,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:36:06","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Sun Jun 15 2025 23:36:06 GMT+0000 (Coordinated Universal Time)","process":{"pid":690,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":204894208,"heapTotal":114479104,"heapUsed":46334400,"external":2981377,"arrayBuffers":120120}},"os":{"loadavg":[2.68,2.04,1.83],"uptime":33546.92},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":529,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:36:48","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Sun Jun 15 2025 23:36:48 GMT+0000 (Coordinated Universal Time)","process":{"pid":725,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":204984320,"heapTotal":114479104,"heapUsed":45527952,"external":2980538,"arrayBuffers":119281}},"os":{"loadavg":[1.33,1.77,1.75],"uptime":33588.93},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":529,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:37:00","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Sun Jun 15 2025 23:37:00 GMT+0000 (Coordinated Universal Time)","process":{"pid":758,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":200577024,"heapTotal":111857664,"heapUsed":46232408,"external":2982586,"arrayBuffers":119281}},"os":{"loadavg":[1.37,1.76,1.74],"uptime":33600.92},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":529,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:37:14","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Sun Jun 15 2025 23:37:14 GMT+0000 (Coordinated Universal Time)","process":{"pid":792,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":202145792,"heapTotal":114216960,"heapUsed":45533088,"external":2980538,"arrayBuffers":119281}},"os":{"loadavg":[1.55,1.78,1.75],"uptime":33614.96},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":529,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:37:30","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Sun Jun 15 2025 23:37:30 GMT+0000 (Coordinated Universal Time)","process":{"pid":826,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":199118848,"heapTotal":112644096,"heapUsed":45605824,"external":2980538,"arrayBuffers":119281}},"os":{"loadavg":[2.24,1.92,1.8],"uptime":33630.95},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":529,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:37:46","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Sun Jun 15 2025 23:37:46 GMT+0000 (Coordinated Universal Time)","process":{"pid":860,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":203993088,"heapTotal":113430528,"heapUsed":45518784,"external":2980550,"arrayBuffers":119293}},"os":{"loadavg":[2.79,2.05,1.84],"uptime":33646.93},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":529,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:38:06","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Sun Jun 15 2025 23:38:06 GMT+0000 (Coordinated Universal Time)","process":{"pid":894,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":196874240,"heapTotal":113430528,"heapUsed":47458120,"external":2983425,"arrayBuffers":120120}},"os":{"loadavg":[3.2,2.2,1.89],"uptime":33666.93},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":529,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:38:16","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Sun Jun 15 2025 23:38:16 GMT+0000 (Coordinated Universal Time)","process":{"pid":928,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":197201920,"heapTotal":112644096,"heapUsed":45534208,"external":2980538,"arrayBuffers":119281}},"os":{"loadavg":[2.86,2.16,1.89],"uptime":33676.95},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":529,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:38:31","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Sun Jun 15 2025 23:38:31 GMT+0000 (Coordinated Universal Time)","process":{"pid":962,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":201580544,"heapTotal":113692672,"heapUsed":45851264,"external":2980538,"arrayBuffers":119281}},"os":{"loadavg":[2.36,2.08,1.87],"uptime":33691.93},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":529,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:38:47","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Sun Jun 15 2025 23:38:47 GMT+0000 (Coordinated Universal Time)","process":{"pid":996,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":201338880,"heapTotal":113430528,"heapUsed":46833824,"external":2982586,"arrayBuffers":119281}},"os":{"loadavg":[1.69,1.95,1.83],"uptime":33707.92},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":529,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:39:00","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Sun Jun 15 2025 23:39:00 GMT+0000 (Coordinated Universal Time)","process":{"pid":1030,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":202674176,"heapTotal":113954816,"heapUsed":46396512,"external":2981377,"arrayBuffers":120120}},"os":{"loadavg":[1.59,1.91,1.82],"uptime":33720.93},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":529,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:39:15","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Sun Jun 15 2025 23:39:15 GMT+0000 (Coordinated Universal Time)","process":{"pid":1064,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":201932800,"heapTotal":113692672,"heapUsed":45605664,"external":2980538,"arrayBuffers":119281}},"os":{"loadavg":[1.32,1.84,1.79],"uptime":33735.93},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":529,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:39:31","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Sun Jun 15 2025 23:39:31 GMT+0000 (Coordinated Universal Time)","process":{"pid":1098,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":201916416,"heapTotal":113430528,"heapUsed":45595384,"external":2980538,"arrayBuffers":119281}},"os":{"loadavg":[1.03,1.75,1.76],"uptime":33751.96},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":529,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:40:09","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Sun Jun 15 2025 23:40:09 GMT+0000 (Coordinated Universal Time)","process":{"pid":1132,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":197427200,"heapTotal":112644096,"heapUsed":45624376,"external":2980538,"arrayBuffers":119281}},"os":{"loadavg":[1,1.62,1.72],"uptime":33789.95},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":529,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:40:43","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Sun Jun 15 2025 23:40:43 GMT+0000 (Coordinated Universal Time)","process":{"pid":1167,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":196734976,"heapTotal":112644096,"heapUsed":45559528,"external":2980538,"arrayBuffers":119281}},"os":{"loadavg":[1.84,1.79,1.77],"uptime":33823.95},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":529,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:40:54","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Sun Jun 15 2025 23:40:54 GMT+0000 (Coordinated Universal Time)","process":{"pid":1200,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":203276288,"heapTotal":114216960,"heapUsed":45413544,"external":2979942,"arrayBuffers":118685}},"os":{"loadavg":[1.63,1.75,1.76],"uptime":33834.94},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":529,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:41:12","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Sun Jun 15 2025 23:41:12 GMT+0000 (Coordinated Universal Time)","process":{"pid":1234,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":203546624,"heapTotal":114479104,"heapUsed":46360304,"external":2981361,"arrayBuffers":120104}},"os":{"loadavg":[1.25,1.65,1.72],"uptime":33852.97},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":529,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:41:39","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Sun Jun 15 2025 23:41:39 GMT+0000 (Coordinated Universal Time)","process":{"pid":127,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":197505024,"heapTotal":111857664,"heapUsed":45655520,"external":2980538,"arrayBuffers":119281}},"os":{"loadavg":[1.5,1.66,1.73],"uptime":33879.97},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":529,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:42:59","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:529:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Sun Jun 15 2025 23:42:59 GMT+0000 (Coordinated Universal Time)","process":{"pid":128,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":203640832,"heapTotal":112119808,"heapUsed":45575000,"external":2980538,"arrayBuffers":119281}},"os":{"loadavg":[1.06,1.48,1.66],"uptime":33959.96},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":529,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
