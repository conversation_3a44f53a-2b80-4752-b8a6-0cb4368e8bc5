// performance-test.js
require('dotenv').config();
const cacheService = require('../../src/services/cacheService');

// Test utilities
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logHeader(message) {
  log(`\n${colors.bold}=== ${message} ===${colors.reset}`, 'blue');
}

// Performance measurement utility
function measureTime(fn) {
  return async (...args) => {
    const start = process.hrtime.bigint();
    const result = await fn(...args);
    const end = process.hrtime.bigint();
    const duration = Number(end - start) / 1000000; // Convert to milliseconds
    return { result, duration };
  };
}

// Generate test data of different sizes
function generateTestData(size) {
  const sizes = {
    small: 10,
    medium: 100,
    large: 1000,
    xlarge: 10000
  };
  
  const count = sizes[size] || 100;
  
  return {
    metadata: {
      size,
      count,
      generated: new Date().toISOString()
    },
    data: Array.from({length: count}, (_, i) => ({
      id: i,
      name: `Item ${i}`,
      description: `Descrição detalhada do item ${i} com informações relevantes para teste de performance`,
      category: `categoria-${i % 10}`,
      tags: [`tag-${i}`, `type-${i % 5}`, `group-${Math.floor(i / 10)}`],
      properties: {
        active: i % 2 === 0,
        priority: i % 3,
        score: Math.random() * 100,
        created: new Date(Date.now() - Math.random() * 86400000 * 30).toISOString()
      },
      nested: {
        level1: {
          level2: {
            level3: {
              value: `nested-value-${i}`,
              data: Array.from({length: 5}, (_, j) => `nested-${i}-${j}`)
            }
          }
        }
      }
    }))
  };
}

class PerformanceTestSuite {
  constructor() {
    this.results = [];
  }

  async runPerformanceTest(testName, testFn, iterations = 1) {
    logInfo(`Executando teste de performance: ${testName}`);
    
    const times = [];
    let errors = 0;
    
    for (let i = 0; i < iterations; i++) {
      try {
        const { result, duration } = await measureTime(testFn);
        times.push(duration);
        
        if (i === 0) {
          logInfo(`Primeira execução: ${duration.toFixed(2)}ms`);
        }
      } catch (error) {
        errors++;
        logError(`Erro na iteração ${i + 1}: ${error.message}`);
      }
    }
    
    if (times.length === 0) {
      logError(`Teste ${testName} falhou completamente`);
      return;
    }
    
    const avg = times.reduce((a, b) => a + b, 0) / times.length;
    const min = Math.min(...times);
    const max = Math.max(...times);
    const median = times.sort((a, b) => a - b)[Math.floor(times.length / 2)];
    
    const result = {
      testName,
      iterations: times.length,
      errors,
      avg: avg.toFixed(2),
      min: min.toFixed(2),
      max: max.toFixed(2),
      median: median.toFixed(2)
    };
    
    this.results.push(result);
    
    logSuccess(`${testName} - Média: ${avg.toFixed(2)}ms, Min: ${min.toFixed(2)}ms, Max: ${max.toFixed(2)}ms`);
    
    return result;
  }

  // Test 1: Basic SET/GET Performance
  async testBasicOperations() {
    const testData = generateTestData('medium');
    const key = 'perf:basic:test';
    
    await this.runPerformanceTest('SET Operation', async () => {
      return await cacheService.set(key, testData, 60);
    }, 100);
    
    await this.runPerformanceTest('GET Operation', async () => {
      return await cacheService.get(key);
    }, 100);
    
    // Clean up
    await cacheService.delete(key);
  }

  // Test 2: Different Data Sizes
  async testDataSizes() {
    const sizes = ['small', 'medium', 'large', 'xlarge'];
    
    for (const size of sizes) {
      const testData = generateTestData(size);
      const key = `perf:size:${size}`;
      
      await this.runPerformanceTest(`SET ${size.toUpperCase()} data`, async () => {
        return await cacheService.set(key, testData, 60);
      }, 50);
      
      await this.runPerformanceTest(`GET ${size.toUpperCase()} data`, async () => {
        return await cacheService.get(key);
      }, 50);
      
      // Clean up
      await cacheService.delete(key);
    }
  }

  // Test 3: Concurrent Operations
  async testConcurrentOperations() {
    const concurrencyLevels = [10, 50, 100];
    
    for (const level of concurrencyLevels) {
      await this.runPerformanceTest(`Concurrent SET (${level} ops)`, async () => {
        const promises = [];
        for (let i = 0; i < level; i++) {
          const key = `perf:concurrent:set:${i}`;
          const data = generateTestData('small');
          promises.push(cacheService.set(key, data, 60));
        }
        return await Promise.all(promises);
      }, 10);
      
      await this.runPerformanceTest(`Concurrent GET (${level} ops)`, async () => {
        const promises = [];
        for (let i = 0; i < level; i++) {
          const key = `perf:concurrent:set:${i}`;
          promises.push(cacheService.get(key));
        }
        return await Promise.all(promises);
      }, 10);
      
      // Clean up
      await cacheService.clear('perf:concurrent:set:*');
    }
  }

  // Test 4: Compression Performance
  async testCompressionPerformance() {
    const largeData = generateTestData('xlarge');
    const key = 'perf:compression';
    
    // Test normal storage
    await this.runPerformanceTest('SET Large Data (Normal)', async () => {
      return await cacheService.set(key, largeData, 60);
    }, 20);
    
    await this.runPerformanceTest('GET Large Data (Normal)', async () => {
      return await cacheService.get(key);
    }, 20);
    
    await cacheService.delete(key);
    
    // Test compressed storage
    await this.runPerformanceTest('SET Large Data (Compressed)', async () => {
      return await cacheService.setCompressed(key, largeData, 60, true);
    }, 20);
    
    await this.runPerformanceTest('GET Large Data (Compressed)', async () => {
      return await cacheService.getCompressed(key);
    }, 20);
    
    // Clean up
    await cacheService.delete(key);
    await cacheService.delete(`${key}:compressed`);
  }

  // Test 5: Pattern Clearing Performance
  async testPatternClearingPerformance() {
    const keyCount = 1000;
    const pattern = 'perf:pattern';
    
    // Create many keys
    logInfo(`Criando ${keyCount} chaves para teste de limpeza...`);
    const createPromises = [];
    for (let i = 0; i < keyCount; i++) {
      const key = `${pattern}:${i}`;
      const data = { index: i, data: `test-${i}` };
      createPromises.push(cacheService.set(key, data, 60));
    }
    await Promise.all(createPromises);
    
    // Test pattern clearing
    await this.runPerformanceTest(`Clear ${keyCount} keys by pattern`, async () => {
      return await cacheService.clear(`${pattern}:*`);
    }, 5);
  }

  // Test 6: getOrSet Performance
  async testGetOrSetPerformance() {
    const key = 'perf:getOrSet';
    let callCount = 0;
    
    const expensiveOperation = async () => {
      callCount++;
      // Simulate expensive operation
      await new Promise(resolve => setTimeout(resolve, 10));
      return generateTestData('medium');
    };
    
    // First call (cache miss)
    await this.runPerformanceTest('getOrSet (Cache Miss)', async () => {
      return await cacheService.getOrSet(key, expensiveOperation, 60);
    }, 10);
    
    // Subsequent calls (cache hit)
    await this.runPerformanceTest('getOrSet (Cache Hit)', async () => {
      return await cacheService.getOrSet(key, expensiveOperation, 60);
    }, 50);
    
    logInfo(`Função cara foi chamada ${callCount} vezes (deveria ser ~10)`);
    
    // Clean up
    await cacheService.delete(key);
  }

  printResults() {
    logHeader('RESULTADOS DOS TESTES DE PERFORMANCE');
    
    console.table(this.results.map(r => ({
      'Teste': r.testName,
      'Iterações': r.iterations,
      'Erros': r.errors,
      'Média (ms)': r.avg,
      'Min (ms)': r.min,
      'Max (ms)': r.max,
      'Mediana (ms)': r.median
    })));
    
    // Performance analysis
    logHeader('ANÁLISE DE PERFORMANCE');
    
    const avgTimes = this.results.map(r => parseFloat(r.avg));
    const overallAvg = avgTimes.reduce((a, b) => a + b, 0) / avgTimes.length;
    
    logInfo(`Tempo médio geral: ${overallAvg.toFixed(2)}ms`);
    
    const slowTests = this.results.filter(r => parseFloat(r.avg) > overallAvg * 2);
    if (slowTests.length > 0) {
      logWarning('Testes mais lentos que a média:');
      slowTests.forEach(test => {
        logWarning(`  - ${test.testName}: ${test.avg}ms`);
      });
    }
    
    const fastTests = this.results.filter(r => parseFloat(r.avg) < 10);
    if (fastTests.length > 0) {
      logSuccess(`${fastTests.length} testes executaram em menos de 10ms`);
    }
  }
}

// Main test execution
async function runPerformanceTests() {
  logHeader('INICIANDO TESTES DE PERFORMANCE DO CACHE');
  
  const testSuite = new PerformanceTestSuite();

  try {
    // Initialize cache service
    logInfo('Inicializando serviço de cache...');
    const initResult = await cacheService.initialize();
    if (!initResult.success) {
      throw new Error(`Falha ao inicializar cache: ${initResult.error}`);
    }
    logSuccess('Serviço de cache inicializado com sucesso');

    // Clean up any existing test data
    await cacheService.clear('perf:*');
    logInfo('Cache limpo para testes');

    // Run performance tests
    await testSuite.testBasicOperations();
    await testSuite.testDataSizes();
    await testSuite.testConcurrentOperations();
    await testSuite.testCompressionPerformance();
    await testSuite.testPatternClearingPerformance();
    await testSuite.testGetOrSetPerformance();

  } catch (error) {
    logError(`Erro durante os testes: ${error.message}`);
  } finally {
    // Print results
    testSuite.printResults();
    
    // Clean up test data
    await cacheService.clear('perf:*');
    
    // Close connection
    try {
      await cacheService.close();
      logInfo('Conexão com cache fechada');
    } catch (error) {
      logWarning(`Erro ao fechar conexão: ${error.message}`);
    }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runPerformanceTests().catch(console.error);
}

module.exports = { PerformanceTestSuite, runPerformanceTests };
