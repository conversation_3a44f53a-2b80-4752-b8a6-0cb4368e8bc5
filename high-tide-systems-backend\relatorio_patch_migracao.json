{"timestamp": "2025-05-30T21:08:53.988Z", "type": "PATCH_MIGRATION", "company": {"id": "9f12a652-52ff-49ae-a930-fdc50a1decbf", "name": "ABA+ Migrada"}, "csvAnalysis": {"totalPacientesCSV": 142, "problemasTrocados": 9, "registrosMapeados": 142}, "statistics": {"relationshipsUpdated": 0, "clientsCreated": 0, "personsCreated": 0, "clientPersonsCreated": 0, "problemasGeneroCorrigidos": 9, "pessoasComMultiplosResponsaveis": 78, "pacientesEncontrados": 122, "responsaveisEncontrados": 21, "pacientesNaoEncontrados": 202, "responsaveisNaoEncontrados": 1}, "corrections": ["✅ 0 relacionamentos atualizados ('mae'/'pai'→'dependente', 'self'→'titular')", "✅ 0 Persons criadas para responsáveis que não tinham dados próprios", "✅ 0 Clients criados para pais que estavam faltando", "✅ 0 relacionamentos Client-Person criados", "✅ 9 problemas de gênero detectados e corrigidos", "✅ Estrutura de dados normalizada para o frontend", "✅ 122 pacientes mapeados corretamente", "✅ Lucas responsáveis não encontrados no CSV (normal)"], "warnings": ["Dados existentes foram preservados e corrigidos", "Novos Clients criados com senha padrão: DDCHighTide", "Responsáveis não encontrados no CSV é situação normal", "CSV contém apenas pacientes, não todos os responsáveis", "Verificar se todos os relacionamentos estão corretos"], "problematicRecords": {"pacientesNaoEncontrados": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Testestes", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Anacarolinadaco", "Anaclaralisboa", "Isabela.pmata", "Psiregian<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "total": 202}}