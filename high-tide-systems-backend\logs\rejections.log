{"timestamp":"2025-06-15 23:01:15","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Sun Jun 15 2025 23:01:15 GMT+0000 (Coordinated Universal Time)","process":{"pid":128,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":151879680,"heapTotal":49991680,"heapUsed":46860184,"external":2998945,"arrayBuffers":135640}},"os":{"loadavg":[0.82,1.43,1.54],"uptime":31455.99},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:14:33","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Sun Jun 15 2025 23:14:33 GMT+0000 (Coordinated Universal Time)","process":{"pid":356,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":198258688,"heapTotal":111857664,"heapUsed":45271016,"external":2980474,"arrayBuffers":119257}},"os":{"loadavg":[2.45,1.74,1.65],"uptime":32253.89},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:14:33","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Sun Jun 15 2025 23:14:33 GMT+0000 (Coordinated Universal Time)","process":{"pid":356,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":198258688,"heapTotal":111857664,"heapUsed":45719912,"external":2980538,"arrayBuffers":119281}},"os":{"loadavg":[2.45,1.74,1.65],"uptime":32253.89},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:15:08","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Sun Jun 15 2025 23:15:08 GMT+0000 (Coordinated Universal Time)","process":{"pid":390,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":197611520,"heapTotal":111595520,"heapUsed":44865656,"external":2979635,"arrayBuffers":118418}},"os":{"loadavg":[2.88,1.94,1.73],"uptime":32288.88},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:19:37","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Sun Jun 15 2025 23:19:37 GMT+0000 (Coordinated Universal Time)","process":{"pid":542,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":199708672,"heapTotal":112381952,"heapUsed":44849240,"external":2979635,"arrayBuffers":118418}},"os":{"loadavg":[2.14,1.86,1.74],"uptime":32557.89},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:36:05","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Sun Jun 15 2025 23:36:05 GMT+0000 (Coordinated Universal Time)","process":{"pid":690,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":204824576,"heapTotal":114216960,"heapUsed":45137416,"external":2980474,"arrayBuffers":119257}},"os":{"loadavg":[2.68,2.04,1.83],"uptime":33545.94},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:36:05","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Sun Jun 15 2025 23:36:05 GMT+0000 (Coordinated Universal Time)","process":{"pid":690,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":204824576,"heapTotal":114216960,"heapUsed":45320656,"external":2980538,"arrayBuffers":119281}},"os":{"loadavg":[2.68,2.04,1.83],"uptime":33545.94},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:36:47","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Sun Jun 15 2025 23:36:47 GMT+0000 (Coordinated Universal Time)","process":{"pid":725,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":203735040,"heapTotal":114479104,"heapUsed":44731432,"external":2979635,"arrayBuffers":118418}},"os":{"loadavg":[1.33,1.77,1.75],"uptime":33587.94},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:36:59","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Sun Jun 15 2025 23:36:59 GMT+0000 (Coordinated Universal Time)","process":{"pid":758,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":198959104,"heapTotal":111857664,"heapUsed":45561608,"external":2981683,"arrayBuffers":118418}},"os":{"loadavg":[1.37,1.76,1.74],"uptime":33599.94},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:37:14","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Sun Jun 15 2025 23:37:14 GMT+0000 (Coordinated Universal Time)","process":{"pid":792,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":201445376,"heapTotal":114216960,"heapUsed":44702992,"external":2979635,"arrayBuffers":118418}},"os":{"loadavg":[1.55,1.78,1.75],"uptime":33614.94},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:37:30","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Sun Jun 15 2025 23:37:30 GMT+0000 (Coordinated Universal Time)","process":{"pid":826,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":198553600,"heapTotal":112644096,"heapUsed":44890368,"external":2979635,"arrayBuffers":118418}},"os":{"loadavg":[2.24,1.92,1.8],"uptime":33630.94},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:37:45","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Sun Jun 15 2025 23:37:45 GMT+0000 (Coordinated Universal Time)","process":{"pid":860,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":202797056,"heapTotal":113430528,"heapUsed":44667512,"external":2979647,"arrayBuffers":118430}},"os":{"loadavg":[2.79,2.05,1.84],"uptime":33645.94},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:38:05","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Sun Jun 15 2025 23:38:05 GMT+0000 (Coordinated Universal Time)","process":{"pid":894,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":196583424,"heapTotal":113430528,"heapUsed":46708984,"external":2982522,"arrayBuffers":119257}},"os":{"loadavg":[3.2,2.2,1.89],"uptime":33665.95},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:38:05","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Sun Jun 15 2025 23:38:05 GMT+0000 (Coordinated Universal Time)","process":{"pid":894,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":196583424,"heapTotal":113430528,"heapUsed":46845896,"external":2982586,"arrayBuffers":119281}},"os":{"loadavg":[3.2,2.2,1.89],"uptime":33665.95},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:38:16","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Sun Jun 15 2025 23:38:16 GMT+0000 (Coordinated Universal Time)","process":{"pid":928,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":196632576,"heapTotal":112644096,"heapUsed":44855512,"external":2979635,"arrayBuffers":118418}},"os":{"loadavg":[2.86,2.16,1.89],"uptime":33676.94},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:38:30","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Sun Jun 15 2025 23:38:30 GMT+0000 (Coordinated Universal Time)","process":{"pid":962,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":200933376,"heapTotal":113692672,"heapUsed":45033616,"external":2979635,"arrayBuffers":118418}},"os":{"loadavg":[2.36,2.08,1.87],"uptime":33690.94},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:38:45","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Sun Jun 15 2025 23:38:45 GMT+0000 (Coordinated Universal Time)","process":{"pid":996,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":200519680,"heapTotal":113430528,"heapUsed":46103720,"external":2981683,"arrayBuffers":118418}},"os":{"loadavg":[1.84,1.98,1.84],"uptime":33705.94},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:38:59","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Sun Jun 15 2025 23:38:59 GMT+0000 (Coordinated Universal Time)","process":{"pid":1030,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":202579968,"heapTotal":113692672,"heapUsed":45204328,"external":2980474,"arrayBuffers":119257}},"os":{"loadavg":[1.59,1.91,1.82],"uptime":33719.95},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:38:59","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Sun Jun 15 2025 23:38:59 GMT+0000 (Coordinated Universal Time)","process":{"pid":1030,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":202579968,"heapTotal":113692672,"heapUsed":45439264,"external":2980538,"arrayBuffers":119281}},"os":{"loadavg":[1.59,1.91,1.82],"uptime":33719.95},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:39:14","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Sun Jun 15 2025 23:39:14 GMT+0000 (Coordinated Universal Time)","process":{"pid":1064,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":200949760,"heapTotal":113692672,"heapUsed":44883776,"external":2979635,"arrayBuffers":118418}},"os":{"loadavg":[1.32,1.84,1.79],"uptime":33734.93},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:39:31","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Sun Jun 15 2025 23:39:31 GMT+0000 (Coordinated Universal Time)","process":{"pid":1098,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":201916416,"heapTotal":113430528,"heapUsed":45212888,"external":2980474,"arrayBuffers":119257}},"os":{"loadavg":[1.03,1.75,1.76],"uptime":33751.96},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:40:09","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Sun Jun 15 2025 23:40:09 GMT+0000 (Coordinated Universal Time)","process":{"pid":1132,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":196861952,"heapTotal":112644096,"heapUsed":44983576,"external":2980203,"arrayBuffers":118986}},"os":{"loadavg":[1,1.62,1.72],"uptime":33789.94},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:40:43","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Sun Jun 15 2025 23:40:43 GMT+0000 (Coordinated Universal Time)","process":{"pid":1167,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":196169728,"heapTotal":112644096,"heapUsed":44780016,"external":2980203,"arrayBuffers":118986}},"os":{"loadavg":[1.84,1.79,1.77],"uptime":33823.94},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:40:53","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Sun Jun 15 2025 23:40:53 GMT+0000 (Coordinated Universal Time)","process":{"pid":1200,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":203227136,"heapTotal":114216960,"heapUsed":44603872,"external":2979039,"arrayBuffers":117822}},"os":{"loadavg":[1.63,1.75,1.76],"uptime":33833.95},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:41:12","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Sun Jun 15 2025 23:41:12 GMT+0000 (Coordinated Universal Time)","process":{"pid":1234,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":203051008,"heapTotal":114216960,"heapUsed":45297968,"external":2972266,"arrayBuffers":111049}},"os":{"loadavg":[1.25,1.65,1.72],"uptime":33852.96},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:41:12","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Sun Jun 15 2025 23:41:12 GMT+0000 (Coordinated Universal Time)","process":{"pid":1234,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":203051008,"heapTotal":114216960,"heapUsed":45446968,"external":2980522,"arrayBuffers":119265}},"os":{"loadavg":[1.25,1.65,1.72],"uptime":33852.96},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:41:39","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Sun Jun 15 2025 23:41:39 GMT+0000 (Coordinated Universal Time)","process":{"pid":127,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":196513792,"heapTotal":111857664,"heapUsed":44866032,"external":2971443,"arrayBuffers":110226}},"os":{"loadavg":[1.5,1.66,1.73],"uptime":33879.95},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-15 23:42:59","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Sun Jun 15 2025 23:42:59 GMT+0000 (Coordinated Universal Time)","process":{"pid":128,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v18.20.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":202727424,"heapTotal":112119808,"heapUsed":44825344,"external":2971443,"arrayBuffers":110226}},"os":{"loadavg":[1.06,1.48,1.66],"uptime":33959.95},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
