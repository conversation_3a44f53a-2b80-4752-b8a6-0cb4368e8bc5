// src/middlewares/sessionCache.js
const cacheService = require('../services/cacheService');
const loggerService = require('../services/loggerService');

/**
 * Middleware para cache de dados de sessão do usuário
 * Otimiza consultas frequentes de dados do usuário logado
 */
class SessionCacheMiddleware {
  /**
   * Cache de dados básicos do usuário
   * @param {number} ttl - Tempo de vida em segundos (padrão: 30 minutos)
   * @returns {Function} - Middleware Express
   */
  static userDataCache(ttl = 1800) {
    return async (req, res, next) => {
      if (!req.user?.id) {
        return next();
      }

      const requestLogger = req.logger || loggerService.child({
        middleware: 'sessionCache.userData',
        userId: req.user.id
      });

      try {
        const cacheKey = `session:user:${req.user.id}`;
        
        // Buscar dados do usuário no cache
        const cachedUserData = await cacheService.get(cacheKey);
        
        if (cachedUserData) {
          // Adicionar dados do cache ao request
          req.cachedUser = cachedUserData;
          requestLogger.cache('GET', cacheKey, true, 0);
          requestLogger.debug('USER_DATA_CACHE_HIT', { userId: req.user.id });
        } else {
          // Marcar para buscar dados do banco
          req.needsUserDataCache = true;
          requestLogger.cache('GET', cacheKey, false, 0);
          requestLogger.debug('USER_DATA_CACHE_MISS', { userId: req.user.id });
        }

        // Interceptar resposta para cachear dados do usuário se necessário
        const originalJson = res.json;
        res.json = function(data) {
          res.json = originalJson;

          // Se a resposta contém dados do usuário, cachear
          if (req.needsUserDataCache && data.user) {
            cacheService.set(cacheKey, data.user, ttl)
              .then(() => {
                requestLogger.cache('SET', cacheKey, false, 0);
                requestLogger.debug('USER_DATA_CACHED', { userId: req.user.id });
              })
              .catch(err => {
                requestLogger.error('USER_DATA_CACHE_ERROR', {
                  error: err.message,
                  userId: req.user.id
                });
              });
          }

          return originalJson.call(this, data);
        };

        next();
      } catch (error) {
        requestLogger.error('SESSION_CACHE_ERROR', {
          error: error.message,
          stack: error.stack
        });
        next();
      }
    };
  }

  /**
   * Cache de permissões do usuário
   * @param {number} ttl - Tempo de vida em segundos (padrão: 1 hora)
   * @returns {Function} - Middleware Express
   */
  static userPermissionsCache(ttl = 3600) {
    return async (req, res, next) => {
      if (!req.user?.id) {
        return next();
      }

      const requestLogger = req.logger || loggerService.child({
        middleware: 'sessionCache.permissions',
        userId: req.user.id
      });

      try {
        const cacheKey = `session:permissions:${req.user.id}`;
        
        // Buscar permissões no cache
        const cachedPermissions = await cacheService.get(cacheKey);
        
        if (cachedPermissions) {
          req.cachedPermissions = cachedPermissions;
          requestLogger.cache('GET', cacheKey, true, 0);
          requestLogger.debug('PERMISSIONS_CACHE_HIT', { userId: req.user.id });
        } else {
          requestLogger.cache('GET', cacheKey, false, 0);
          requestLogger.debug('PERMISSIONS_CACHE_MISS', { userId: req.user.id });
          
          // Buscar permissões do banco e cachear
          if (req.user.permissions) {
            await cacheService.set(cacheKey, req.user.permissions, ttl);
            req.cachedPermissions = req.user.permissions;
            requestLogger.cache('SET', cacheKey, false, 0);
          }
        }

        next();
      } catch (error) {
        requestLogger.error('PERMISSIONS_CACHE_ERROR', {
          error: error.message,
          stack: error.stack
        });
        next();
      }
    };
  }

  /**
   * Cache de configurações do usuário
   * @param {number} ttl - Tempo de vida em segundos (padrão: 2 horas)
   * @returns {Function} - Middleware Express
   */
  static userPreferencesCache(ttl = 7200) {
    return async (req, res, next) => {
      if (!req.user?.id) {
        return next();
      }

      const requestLogger = req.logger || loggerService.child({
        middleware: 'sessionCache.preferences',
        userId: req.user.id
      });

      try {
        const cacheKey = `session:preferences:${req.user.id}`;
        
        const cachedPreferences = await cacheService.get(cacheKey);
        
        if (cachedPreferences) {
          req.cachedPreferences = cachedPreferences;
          requestLogger.cache('GET', cacheKey, true, 0);
        } else {
          requestLogger.cache('GET', cacheKey, false, 0);
        }

        next();
      } catch (error) {
        requestLogger.error('PREFERENCES_CACHE_ERROR', {
          error: error.message,
          stack: error.stack
        });
        next();
      }
    };
  }

  /**
   * Cache de dados da empresa do usuário
   * @param {number} ttl - Tempo de vida em segundos (padrão: 1 hora)
   * @returns {Function} - Middleware Express
   */
  static companyDataCache(ttl = 3600) {
    return async (req, res, next) => {
      if (!req.user?.companyId) {
        return next();
      }

      const requestLogger = req.logger || loggerService.child({
        middleware: 'sessionCache.company',
        companyId: req.user.companyId
      });

      try {
        const cacheKey = `session:company:${req.user.companyId}`;
        
        const cachedCompany = await cacheService.get(cacheKey);
        
        if (cachedCompany) {
          req.cachedCompany = cachedCompany;
          requestLogger.cache('GET', cacheKey, true, 0);
          requestLogger.debug('COMPANY_CACHE_HIT', { companyId: req.user.companyId });
        } else {
          requestLogger.cache('GET', cacheKey, false, 0);
          requestLogger.debug('COMPANY_CACHE_MISS', { companyId: req.user.companyId });
        }

        next();
      } catch (error) {
        requestLogger.error('COMPANY_CACHE_ERROR', {
          error: error.message,
          stack: error.stack
        });
        next();
      }
    };
  }

  /**
   * Invalidar cache de sessão do usuário
   * @param {string} userId - ID do usuário
   * @returns {Function} - Middleware Express
   */
  static invalidateUserSession(userId = null) {
    return async (req, res, next) => {
      const targetUserId = userId || req.params.id || req.user?.id;
      
      if (!targetUserId) {
        return next();
      }

      const requestLogger = req.logger || loggerService.child({
        middleware: 'sessionCache.invalidate',
        targetUserId
      });

      // Interceptar resposta para invalidar cache após sucesso
      const originalJson = res.json;
      res.json = function(data) {
        res.json = originalJson;

        if (res.statusCode >= 200 && res.statusCode < 300) {
          // Invalidar todos os caches de sessão do usuário
          const patterns = [
            `session:user:${targetUserId}`,
            `session:permissions:${targetUserId}`,
            `session:preferences:${targetUserId}`
          ];

          Promise.all(patterns.map(pattern => cacheService.delete(pattern)))
            .then(() => {
              requestLogger.info('USER_SESSION_CACHE_INVALIDATED', {
                userId: targetUserId,
                patterns
              });
            })
            .catch(err => {
              requestLogger.error('SESSION_CACHE_INVALIDATION_ERROR', {
                error: err.message,
                userId: targetUserId
              });
            });
        }

        return originalJson.call(this, data);
      };

      next();
    };
  }

  /**
   * Cache combinado de sessão (dados + permissões + empresa)
   * @param {Object} options - Opções de configuração
   * @returns {Function} - Middleware Express
   */
  static fullSessionCache(options = {}) {
    const {
      userTTL = 1800,
      permissionsTTL = 3600,
      companyTTL = 3600
    } = options;

    return [
      this.userDataCache(userTTL),
      this.userPermissionsCache(permissionsTTL),
      this.companyDataCache(companyTTL)
    ];
  }
}

module.exports = SessionCacheMiddleware;
