"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/users/page",{

/***/ "(app-pages-browser)/./src/app/modules/admin/users/UsersPage.js":
/*!**************************************************!*\
  !*** ./src/app/modules/admin/users/UsersPage.js ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-cog.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ExportMenu */ \"(app-pages-browser)/./src/components/ui/ExportMenu.js\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/usePermissions */ \"(app-pages-browser)/./src/hooks/usePermissions.js\");\n/* harmony import */ var _components_permissions_Protected__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/permissions/Protected */ \"(app-pages-browser)/./src/components/permissions/Protected.js\");\n/* harmony import */ var _components_users_UserFormModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/users/UserFormModal */ \"(app-pages-browser)/./src/components/users/UserFormModal.js\");\n/* harmony import */ var _components_users_ModulesModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/users/ModulesModal */ \"(app-pages-browser)/./src/components/users/ModulesModal.js\");\n/* harmony import */ var _components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.js\");\n/* harmony import */ var _components_permissions_PermissionsModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/permissions/PermissionsModal */ \"(app-pages-browser)/./src/components/permissions/PermissionsModal.js\");\n/* harmony import */ var _components_users_RoleModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/users/RoleModal */ \"(app-pages-browser)/./src/components/users/RoleModal.js\");\n/* harmony import */ var _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/modules/admin/services/userService */ \"(app-pages-browser)/./src/app/modules/admin/services/userService.js\");\n/* harmony import */ var _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/modules/admin/services/companyService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyService.js\");\n/* harmony import */ var _services_subscriptionService__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/services/subscriptionService */ \"(app-pages-browser)/./src/services/subscriptionService.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/ModuleHeader */ \"(app-pages-browser)/./src/components/ui/ModuleHeader.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst UsersPage = ()=>{\n    var _subscriptionData_usage;\n    _s();\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { can } = (0,_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__.usePermissions)();\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [totalUsers, setTotalUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [moduleFilter, setModuleFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [companyFilter, setCompanyFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [usersFilter, setUsersFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [userOptions, setUserOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingUserOptions, setIsLoadingUserOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingCompanies, setIsLoadingCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userFormOpen, setUserFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedUser, setSelectedUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modulesModalOpen, setModulesModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [confirmationDialogOpen, setConfirmationDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [permissionsModalOpen, setPermissionsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [roleModalOpen, setRoleModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [actionToConfirm, setActionToConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [subscriptionData, setSubscriptionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingSubscription, setIsLoadingSubscription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Constantes\n    const ITEMS_PER_PAGE = 10;\n    const MODULE_LABELS = {\n        ADMIN: \"Administração\",\n        RH: \"RH\",\n        FINANCIAL: \"Financeiro\",\n        SCHEDULING: \"Agendamento\",\n        BASIC: \"Básico\"\n    };\n    // Verificar se o usuário atual é um system_admin ou company_admin\n    const isSystemAdmin = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"SYSTEM_ADMIN\";\n    const isAdmin = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"SYSTEM_ADMIN\" || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"COMPANY_ADMIN\";\n    // Verificar se pode adicionar usuários baseado no limite da subscription\n    const canAddUsers = (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData_usage = subscriptionData.usage) === null || _subscriptionData_usage === void 0 ? void 0 : _subscriptionData_usage.canAddUsers) !== false;\n    // Função para carregar empresas (apenas para system_admin)\n    const loadCompanies = async ()=>{\n        if (!isSystemAdmin) return;\n        setIsLoadingCompanies(true);\n        try {\n            const response = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_13__.companyService.getCompaniesForSelect();\n            setCompanies(response);\n        } catch (error) {\n            console.error(\"Erro ao carregar empresas:\", error);\n        } finally{\n            setIsLoadingCompanies(false);\n        }\n    };\n    // Função para carregar dados da subscription\n    const loadSubscriptionData = async ()=>{\n        if (isSystemAdmin) return; // System admin não tem limite de usuários\n        setIsLoadingSubscription(true);\n        try {\n            const response = await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_14__.subscriptionService.getSubscription();\n            setSubscriptionData(response);\n        } catch (error) {\n            console.error(\"Erro ao carregar dados da subscription:\", error);\n            // Se não conseguir carregar, assume que pode adicionar usuários\n            setSubscriptionData({\n                usage: {\n                    canAddUsers: true\n                }\n            });\n        } finally{\n            setIsLoadingSubscription(false);\n        }\n    };\n    // Função para carregar opções de usuários para o multi-select\n    const loadUserOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"UsersPage.useCallback[loadUserOptions]\": async ()=>{\n            setIsLoadingUserOptions(true);\n            try {\n                // Carregar todos os usuários para o multi-select (com limite maior)\n                const response = await _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_12__.userService.list(1, 100, {\n                    active: true // Apenas usuários ativos por padrão\n                });\n                // Transformar os dados para o formato esperado pelo MultiSelect\n                const options = response.users.map({\n                    \"UsersPage.useCallback[loadUserOptions].options\": (user)=>({\n                            value: user.id,\n                            label: user.fullName\n                        })\n                }[\"UsersPage.useCallback[loadUserOptions].options\"]);\n                setUserOptions(options);\n            } catch (error) {\n                console.error(\"Erro ao carregar opções de usuários:\", error);\n            } finally{\n                setIsLoadingUserOptions(false);\n            }\n        }\n    }[\"UsersPage.useCallback[loadUserOptions]\"], []);\n    const loadUsers = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : currentPage, searchQuery = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : search, status = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : statusFilter, module = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : moduleFilter, company = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : companyFilter, userIds = arguments.length > 5 && arguments[5] !== void 0 ? arguments[5] : usersFilter, sortField = arguments.length > 6 && arguments[6] !== void 0 ? arguments[6] : \"fullName\", sortDirection = arguments.length > 7 && arguments[7] !== void 0 ? arguments[7] : \"asc\";\n        setIsLoading(true);\n        try {\n            const filters = {\n                search: searchQuery || undefined,\n                active: status === \"\" ? undefined : status === \"active\",\n                module: module || undefined,\n                // Adiciona parâmetro para filtrar system_admin quando o usuário atual não for system_admin\n                excludeSystemAdmin: !isSystemAdmin,\n                // Adiciona parâmetro para filtrar por empresa (apenas para system_admin)\n                companyId: company || undefined,\n                // Adiciona parâmetro para filtrar por IDs específicos de usuários\n                userIds: userIds.length > 0 ? userIds : undefined,\n                // Adiciona parâmetros de ordenação\n                sortField: sortField,\n                sortDirection: sortDirection\n            };\n            const response = await _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_12__.userService.list(page, ITEMS_PER_PAGE, filters);\n            setUsers(response.users);\n            setTotalUsers(response.total);\n            setTotalPages(response.pages);\n            setCurrentPage(page);\n        } catch (error) {\n            console.error(\"Erro ao carregar usuários:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UsersPage.useEffect\": ()=>{\n            loadUsers();\n            // Carregar empresas se o usuário for system_admin\n            if (isSystemAdmin) {\n                loadCompanies();\n            }\n            // Carregar dados da subscription para verificar limite de usuários\n            loadSubscriptionData();\n            // Carregar opções de usuários para o multi-select\n            loadUserOptions();\n        }\n    }[\"UsersPage.useEffect\"], [\n        loadUserOptions\n    ]);\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        loadUsers(1, search, statusFilter, moduleFilter, companyFilter, usersFilter);\n    };\n    const handleStatusFilterChange = (value)=>{\n        setStatusFilter(value);\n        loadUsers(1, search, value, moduleFilter, companyFilter, usersFilter);\n    };\n    const handleModuleFilterChange = (value)=>{\n        setModuleFilter(value);\n        loadUsers(1, search, statusFilter, value, companyFilter, usersFilter);\n    };\n    const handleCompanyFilterChange = (value)=>{\n        setCompanyFilter(value);\n        loadUsers(1, search, statusFilter, moduleFilter, value, usersFilter);\n    };\n    const handleUsersFilterChange = (value)=>{\n        setUsersFilter(value);\n        loadUsers(1, search, statusFilter, moduleFilter, companyFilter, value);\n    };\n    const handlePageChange = (page)=>{\n        loadUsers(page, search, statusFilter, moduleFilter, companyFilter, usersFilter);\n    };\n    const handleResetFilters = ()=>{\n        setSearch(\"\");\n        setStatusFilter(\"\");\n        setModuleFilter(\"\");\n        setCompanyFilter(\"\");\n        setUsersFilter([]);\n        loadUsers(1, \"\", \"\", \"\", \"\", []);\n    };\n    const handleEditUser = (user)=>{\n        setSelectedUser(user);\n        setUserFormOpen(true);\n    };\n    const handleEditModules = (user)=>{\n        setSelectedUser(user);\n        setModulesModalOpen(true);\n    };\n    const handleManageRole = (user)=>{\n        setSelectedUser(user);\n        setRoleModalOpen(true);\n    };\n    const handleToggleStatus = (user)=>{\n        setSelectedUser(user);\n        setActionToConfirm({\n            type: \"toggle-status\",\n            message: \"\".concat(user.active ? \"Desativar\" : \"Ativar\", \" o usu\\xe1rio \").concat(user.fullName, \"?\")\n        });\n        setConfirmationDialogOpen(true);\n    };\n    const handleDeleteUser = (user)=>{\n        setSelectedUser(user);\n        setActionToConfirm({\n            type: \"delete\",\n            message: \"Excluir permanentemente o usu\\xe1rio \".concat(user.fullName, \"?\")\n        });\n        setConfirmationDialogOpen(true);\n    };\n    const handleManagePermissions = (user)=>{\n        setSelectedUser(user);\n        setPermissionsModalOpen(true);\n    };\n    const handleExport = async (format)=>{\n        setIsExporting(true);\n        try {\n            // Exportar usando os mesmos filtros da tabela atual\n            await _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_12__.userService.exportUsers({\n                search: search || undefined,\n                userIds: usersFilter.length > 0 ? usersFilter : undefined,\n                active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\n                module: moduleFilter || undefined,\n                companyId: companyFilter || undefined\n            }, format);\n        } catch (error) {\n            console.error(\"Erro ao exportar usuários:\", error);\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    const confirmAction = async ()=>{\n        if (actionToConfirm.type === \"toggle-status\") {\n            try {\n                await _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_12__.userService.toggleStatus(selectedUser.id, !selectedUser.active);\n                loadUsers();\n            } catch (error) {\n                console.error(\"Erro ao alterar status do usuário:\", error);\n            }\n        } else if (actionToConfirm.type === \"delete\") {\n            try {\n                await _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_12__.userService.delete(selectedUser.id);\n                loadUsers();\n            } catch (error) {\n                console.error(\"Erro ao excluir usuário:\", error);\n            }\n        }\n        setConfirmationDialogOpen(false);\n    };\n    // Import tutorial steps from tutorialMapping\n    const admUsersTutorialSteps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"UsersPage.useMemo[admUsersTutorialSteps]\": ()=>{\n            // Import dynamically to avoid circular dependencies\n            const tutorialMap = (__webpack_require__(/*! @/tutorials/tutorialMapping */ \"(app-pages-browser)/./src/tutorials/tutorialMapping.js\")[\"default\"]);\n            return tutorialMap['/dashboard/admin/users'] || [];\n        }\n    }[\"UsersPage.useMemo[admUsersTutorialSteps]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-slate-800 dark:text-white flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                size: 24,\n                                className: \"mr-2 text-slate-600 dark:text-slate-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 315,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Usu\\xe1rios\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                        lineNumber: 314,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                onExport: handleExport,\n                                isExporting: isExporting,\n                                disabled: isLoading || users.length === 0,\n                                className: \"text-slate-700 dark:text-slate-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 321,\n                                columnNumber: 11\n                            }, undefined),\n                            can(\"admin.users.create\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            if (canAddUsers) {\n                                                setSelectedUser(null);\n                                                setUserFormOpen(true);\n                                            }\n                                        },\n                                        disabled: !canAddUsers,\n                                        className: \"flex items-center gap-2 px-4 py-2 rounded-lg shadow-md transition-all \".concat(canAddUsers ? \"bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 text-white hover:from-slate-600 hover:to-slate-700 dark:hover:from-slate-700 dark:hover:to-slate-800\" : \"bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                size: 18\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 345,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Novo Usu\\xe1rio\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 346,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                        lineNumber: 331,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    !canAddUsers && (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.usage) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10\",\n                                        children: [\n                                            \"Limite de usu\\xe1rios atingido (\",\n                                            subscriptionData.usage.currentUsers,\n                                            \"/\",\n                                            subscriptionData.usage.userLimit,\n                                            \")\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-800\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 353,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                        lineNumber: 351,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 313,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"Filtros\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                    size: 22,\n                    className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                    lineNumber: 364,\n                    columnNumber: 15\n                }, void 0),\n                description: \"Gerencie os usu\\xe1rios do sistema. Utilize os filtros abaixo para encontrar usu\\xe1rios espec\\xedficos.\",\n                moduleColor: \"admin\",\n                tutorialSteps: admUsersTutorialSteps,\n                tutorialName: \"admin-users-overview\",\n                filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSearch,\n                    className: \"flex flex-col gap-4\",\n                    id: \"filtroUsuario\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 377,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Buscar por nome, email ou login...\",\n                                            value: search,\n                                            onChange: (e)=>setSearch(e.target.value),\n                                            className: \"w-full pl-10 pr-4 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 378,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 376,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full sm:w-40\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.ModuleSelect, {\n                                                moduleColor: \"admin\",\n                                                value: statusFilter,\n                                                onChange: (e)=>handleStatusFilterChange(e.target.value),\n                                                placeholder: \"Status\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Todos os status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"active\",\n                                                        children: \"Ativos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"inactive\",\n                                                        children: \"Inativos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 389,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 388,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full sm:w-48\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.ModuleSelect, {\n                                                moduleColor: \"admin\",\n                                                value: moduleFilter,\n                                                onChange: (e)=>handleModuleFilterChange(e.target.value),\n                                                placeholder: \"M\\xf3dulos\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Todos os m\\xf3dulos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"ADMIN\",\n                                                        children: \"Administra\\xe7\\xe3o\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"RH\",\n                                                        children: \"RH\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"FINANCIAL\",\n                                                        children: \"Financeiro\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"SCHEDULING\",\n                                                        children: \"Agendamento\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"BASIC\",\n                                                        children: \"B\\xe1sico\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 402,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 401,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full sm:w-48\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.ModuleSelect, {\n                                                moduleColor: \"admin\",\n                                                value: companyFilter,\n                                                onChange: (e)=>handleCompanyFilterChange(e.target.value),\n                                                placeholder: \"Empresa\",\n                                                disabled: isLoadingCompanies,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Todas as empresas\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: company.id,\n                                                            children: company.name\n                                                        }, company.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                            lineNumber: 429,\n                                                            columnNumber: 25\n                                                        }, void 0))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 420,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 419,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_16__.FilterButton, {\n                                            type: \"submit\",\n                                            moduleColor: \"admin\",\n                                            variant: \"primary\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"sm:hidden\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 19\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden sm:inline\",\n                                                    children: \"Filtrar\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 19\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 437,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_16__.FilterButton, {\n                                            type: \"button\",\n                                            onClick: handleResetFilters,\n                                            moduleColor: \"admin\",\n                                            variant: \"secondary\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"sm:hidden\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 19\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden sm:inline\",\n                                                    children: \"Limpar\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 19\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 442,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 387,\n                                    columnNumber: 15\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                            lineNumber: 375,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.MultiSelect, {\n                                label: \"Filtrar por Usu\\xe1rios\",\n                                value: usersFilter,\n                                onChange: handleUsersFilterChange,\n                                options: userOptions,\n                                placeholder: \"Selecione um ou mais usu\\xe1rios pelo nome...\",\n                                loading: isLoadingUserOptions,\n                                moduleOverride: \"admin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 456,\n                                columnNumber: 15\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                            lineNumber: 455,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                    lineNumber: 370,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 362,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.ModuleTable, {\n                moduleColor: \"admin\",\n                columns: [\n                    {\n                        header: 'Usuário',\n                        field: 'fullName',\n                        width: '20%'\n                    },\n                    {\n                        header: 'Email',\n                        field: 'email',\n                        width: '15%'\n                    },\n                    {\n                        header: 'Profissão',\n                        field: 'profession',\n                        width: '15%'\n                    },\n                    {\n                        header: 'Módulos',\n                        field: 'modules',\n                        width: '10%'\n                    },\n                    {\n                        header: 'Função',\n                        field: 'role',\n                        width: '10%'\n                    },\n                    {\n                        header: 'Status',\n                        field: 'active',\n                        width: '8%'\n                    },\n                    {\n                        header: 'Ações',\n                        field: 'actions',\n                        className: 'text-right',\n                        width: '14%',\n                        sortable: false\n                    }\n                ],\n                data: users,\n                isLoading: isLoading,\n                emptyMessage: \"Nenhum usu\\xe1rio encontrado\",\n                emptyIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                    lineNumber: 485,\n                    columnNumber: 20\n                }, void 0),\n                currentPage: currentPage,\n                totalPages: totalPages,\n                totalItems: totalUsers,\n                onPageChange: handlePageChange,\n                showPagination: true,\n                tableId: \"admin-users-table\",\n                enableColumnToggle: true,\n                defaultSortField: \"fullName\",\n                defaultSortDirection: \"asc\",\n                onSort: (field, direction)=>{\n                    // Quando a ordenação mudar, recarregar os usuários com os novos parâmetros de ordenação\n                    loadUsers(currentPage, search, statusFilter, moduleFilter, companyFilter, usersFilter, field, direction);\n                },\n                renderRow: (user, index, moduleColors, visibleColumns)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                        className: moduleColors.hoverBg,\n                        children: [\n                            visibleColumns.includes('fullName') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-10 w-10 rounded-full bg-neutral-200 dark:bg-gray-600 flex items-center justify-center text-neutral-600 dark:text-neutral-300 font-medium flex-shrink-0 overflow-hidden\",\n                                            children: user.profileImageFullUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: user.profileImageFullUrl,\n                                                alt: user.fullName,\n                                                className: \"w-full h-full object-cover\",\n                                                onError: (e)=>{\n                                                    e.target.onerror = null;\n                                                    e.target.style.display = 'none';\n                                                    e.target.parentNode.innerHTML = user.fullName.charAt(0).toUpperCase();\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 515,\n                                                columnNumber: 23\n                                            }, void 0) : user.fullName.charAt(0).toUpperCase()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 513,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-neutral-800 dark:text-neutral-100 truncate\",\n                                                    children: user.fullName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 21\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-neutral-500 dark:text-neutral-400 truncate\",\n                                                    children: user.login\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 529,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 512,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 511,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('email') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300 truncate\",\n                                children: user.email\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 542,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('profession') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300\",\n                                children: user.professionObj ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            size: 14,\n                                            className: \"text-neutral-500 dark:text-neutral-400 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 551,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate\",\n                                            children: [\n                                                user.professionObj.name,\n                                                user.professionObj.group && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-neutral-400 dark:text-neutral-500 ml-1\",\n                                                    children: [\n                                                        \"(\",\n                                                        user.professionObj.group.name,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 552,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 550,\n                                    columnNumber: 19\n                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                    children: \"Sem profiss\\xe3o\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 562,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 548,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('modules') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-1\",\n                                    children: [\n                                        user.modules.slice(0, 2).map((module)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 text-xs rounded-full bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-neutral-300\",\n                                                children: MODULE_LABELS[module]\n                                            }, module, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 571,\n                                                columnNumber: 21\n                                            }, void 0)),\n                                        user.modules.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 text-xs rounded-full bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-neutral-300\",\n                                            children: [\n                                                \"+\",\n                                                user.modules.length - 2\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 579,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 569,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 568,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('role') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 text-xs rounded-full inline-flex items-center gap-1 \".concat(user.role === \"SYSTEM_ADMIN\" ? \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\" : user.role === \"COMPANY_ADMIN\" ? \"bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400\" : \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            size: 12,\n                                            className: \"flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 598,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate\",\n                                            children: user.role === \"SYSTEM_ADMIN\" ? \"Admin Sistema\" : user.role === \"COMPANY_ADMIN\" ? \"Admin Empresa\" : \"Funcionário\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 599,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 589,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 588,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('active') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit \".concat(user.active ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\" : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\"),\n                                    children: user.active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                size: 12,\n                                                className: \"flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 621,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Ativo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 622,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                size: 12,\n                                                className: \"flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 626,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Inativo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 627,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 612,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 611,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4 text-right text-sm font-medium\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_6__.Protected, {\n                                            permission: \"admin.users.edit\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleEditUser(user),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\",\n                                                id: \"edicaoUsuario\",\n                                                title: \"Editar\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 644,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 638,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 637,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_6__.Protected, {\n                                            permission: \"admin.permissions.manage\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleEditModules(user),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors\",\n                                                id: \"gerenciarModulo\",\n                                                title: \"Gerenciar m\\xf3dulos\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 655,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 649,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 648,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_6__.Protected, {\n                                            permission: \"admin.permissions.manage\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleManagePermissions(user),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-purple-500 dark:hover:text-purple-400 transition-colors\",\n                                                id: \"gerenciarPermissoes\",\n                                                title: \"Gerenciar permiss\\xf5es\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 666,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 660,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 659,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        (user.role !== \"SYSTEM_ADMIN\" || isSystemAdmin) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_6__.Protected, {\n                                            permission: \"admin.users.edit\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleManageRole(user),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-indigo-500 dark:hover:text-indigo-400 transition-colors\",\n                                                id: \"gerenciarFuncao\",\n                                                title: \"Alterar fun\\xe7\\xe3o\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 673,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 672,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        (user.role !== \"SYSTEM_ADMIN\" || isSystemAdmin) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_6__.Protected, {\n                                            permission: \"admin.users.edit\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleToggleStatus(user),\n                                                className: \"p-1 transition-colors \".concat(user.active ? \"text-neutral-500 dark:text-neutral-400 hover:text-amber-500 dark:hover:text-amber-400\" : \"text-neutral-500 dark:text-neutral-400 hover:text-green-500 dark:hover:text-green-400\"),\n                                                id: \"desativarUsuario\",\n                                                title: user.active ? \"Desativar\" : \"Ativar\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 687,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 686,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        (user.role !== \"SYSTEM_ADMIN\" || isSystemAdmin) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_6__.Protected, {\n                                            permission: \"admin.users.delete\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDeleteUser(user),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors\",\n                                                id: \"excluirUsuario\",\n                                                title: \"Excluir\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 711,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 705,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 704,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 636,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 635,\n                                columnNumber: 15\n                            }, void 0)\n                        ]\n                    }, user.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                        lineNumber: 509,\n                        columnNumber: 11\n                    }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 471,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_UserFormModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: userFormOpen,\n                onClose: ()=>setUserFormOpen(false),\n                user: selectedUser,\n                onSuccess: ()=>{\n                    setUserFormOpen(false);\n                    loadUsers();\n                },\n                currentUser: currentUser\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 723,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_ModulesModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: modulesModalOpen,\n                onClose: ()=>setModulesModalOpen(false),\n                user: selectedUser,\n                onSuccess: ()=>{\n                    setModulesModalOpen(false);\n                    loadUsers();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 734,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: confirmationDialogOpen,\n                onClose: ()=>setConfirmationDialogOpen(false),\n                onConfirm: confirmAction,\n                title: \"Confirmar a\\xe7\\xe3o\",\n                message: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.message) || \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 744,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_PermissionsModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: permissionsModalOpen,\n                onClose: ()=>setPermissionsModalOpen(false),\n                user: selectedUser,\n                onSuccess: ()=>{\n                    setPermissionsModalOpen(false);\n                    loadUsers();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 752,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_RoleModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: roleModalOpen,\n                onClose: ()=>setRoleModalOpen(false),\n                user: selectedUser,\n                onSuccess: ()=>{\n                    setRoleModalOpen(false);\n                    loadUsers();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 761,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n        lineNumber: 311,\n        columnNumber: 5\n    }, undefined);\n};\n_s(UsersPage, \"d8X6bUO97cMyw194FlS92cwYTas=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__.usePermissions\n    ];\n});\n_c = UsersPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UsersPage);\nvar _c;\n$RefreshReg$(_c, \"UsersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/admin/users/UsersPage.js\n"));

/***/ })

});