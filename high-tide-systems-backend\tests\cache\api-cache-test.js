// api-cache-test.js
require('dotenv').config();
const axios = require('axios');
const cacheService = require('../../src/services/cacheService');

// Test configuration
const API_URL = process.env.API_URL || 'http://localhost:5000';
const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'Super@123';

// Test utilities
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logHeader(message) {
  log(`\n${colors.bold}=== ${message} ===${colors.reset}`, 'cyan');
}

// Performance measurement
function measureTime(fn) {
  return async (...args) => {
    const start = Date.now();
    const result = await fn(...args);
    const end = Date.now();
    const duration = end - start;
    return { result, duration };
  };
}

// Get authentication token
async function getAuthToken() {
  try {
    const response = await axios.post(`${API_URL}/api/auth/login`, {
      email: TEST_EMAIL,
      password: TEST_PASSWORD
    });
    
    return response.data.token;
  } catch (error) {
    throw new Error(`Falha na autenticação: ${error.response?.data?.message || error.message}`);
  }
}

// Test cache on real API endpoints
async function testApiCache() {
  logHeader('TESTE DE CACHE EM ROTAS REAIS DA API');
  log('Testando cache em endpoints reais do sistema', 'cyan');
  
  let token;
  
  try {
    // Initialize cache service
    logInfo('Inicializando serviço de cache...');
    await cacheService.initialize();
    logSuccess('Cache inicializado');
    
    // Get authentication token
    logInfo('Obtendo token de autenticação...');
    token = await getAuthToken();
    logSuccess('Token obtido com sucesso');
    
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
    
    // Test endpoints that likely have cache
    const testEndpoints = [
      { 
        name: 'Dashboard Stats', 
        url: '/api/admin/dashboard/stats',
        description: 'Estatísticas do dashboard (provavelmente com cache)'
      },
      { 
        name: 'Health Check', 
        url: '/health',
        description: 'Health check do sistema'
      },
      { 
        name: 'Cache Stats', 
        url: '/health/cache',
        description: 'Estatísticas específicas do cache'
      }
    ];
    
    for (const endpoint of testEndpoints) {
      logHeader(`TESTANDO: ${endpoint.name}`);
      logInfo(endpoint.description);
      
      try {
        // Clear any existing cache for this endpoint
        const cachePattern = endpoint.url.replace(/\//g, ':').replace(/^:/, '') + ':*';
        await cacheService.clear(cachePattern);
        logInfo('Cache limpo para este endpoint');
        
        // First request (cache miss)
        logInfo('Primeira requisição (cache miss)...');
        const { result: firstResponse, duration: firstDuration } = await measureTime(async () => {
          return await axios.get(`${API_URL}${endpoint.url}`, { headers });
        })();
        
        logInfo(`Primeira requisição: ${firstDuration}ms`);
        
        // Check for cache headers
        const cacheHeader = firstResponse.headers['x-cache'];
        if (cacheHeader) {
          logInfo(`Cache header: ${cacheHeader}`);
        }
        
        // Second request (potential cache hit)
        logInfo('Segunda requisição (possível cache hit)...');
        const { result: secondResponse, duration: secondDuration } = await measureTime(async () => {
          return await axios.get(`${API_URL}${endpoint.url}`, { headers });
        })();
        
        logInfo(`Segunda requisição: ${secondDuration}ms`);
        
        // Check for cache headers
        const secondCacheHeader = secondResponse.headers['x-cache'];
        if (secondCacheHeader) {
          logInfo(`Cache header: ${secondCacheHeader}`);
        }
        
        // Analyze performance improvement
        if (secondDuration < firstDuration) {
          const improvement = Math.round((1 - secondDuration / firstDuration) * 100);
          logSuccess(`Melhoria de performance: ${improvement}% (cache funcionando!)`);
        } else if (secondDuration === firstDuration) {
          logWarning('Tempos iguais (cache pode não estar ativo neste endpoint)');
        } else {
          logWarning('Segunda requisição mais lenta (normal em alguns casos)');
        }
        
        // Verify response consistency
        if (JSON.stringify(firstResponse.data) === JSON.stringify(secondResponse.data)) {
          logSuccess('Dados consistentes entre requisições');
        } else {
          logWarning('Dados diferentes entre requisições (pode ser esperado)');
        }
        
      } catch (error) {
        if (error.response?.status === 401) {
          logWarning(`Endpoint requer autenticação específica: ${endpoint.url}`);
        } else if (error.response?.status === 403) {
          logWarning(`Sem permissão para acessar: ${endpoint.url}`);
        } else if (error.response?.status === 404) {
          logWarning(`Endpoint não encontrado: ${endpoint.url}`);
        } else {
          logError(`Erro ao testar ${endpoint.url}: ${error.message}`);
        }
      }
    }
    
    // Test cache statistics
    logHeader('ESTATÍSTICAS FINAIS DO CACHE');
    
    const stats = await cacheService.getStats();
    if (stats) {
      logSuccess('Estatísticas do cache obtidas:');
      logInfo(`Memória usada: ${stats.memory?.used_memory_human || 'N/A'}`);
      logInfo(`Conexões totais: ${stats.stats?.total_connections_received || 'N/A'}`);
      logInfo(`Comandos processados: ${stats.stats?.total_commands_processed || 'N/A'}`);
      logInfo(`Keyspace hits: ${stats.stats?.keyspace_hits || 'N/A'}`);
      logInfo(`Keyspace misses: ${stats.stats?.keyspace_misses || 'N/A'}`);
      
      const hits = parseInt(stats.stats?.keyspace_hits || '0');
      const misses = parseInt(stats.stats?.keyspace_misses || '0');
      const total = hits + misses;
      
      if (total > 0) {
        const hitRate = (hits / total * 100).toFixed(1);
        logInfo(`Taxa de hit do cache: ${hitRate}%`);
        
        if (hitRate > 50) {
          logSuccess('Boa taxa de hit do cache!');
        } else {
          logWarning('Taxa de hit baixa - considere ajustar TTL ou estratégias');
        }
      }
    }
    
    // Test direct cache health endpoint
    try {
      logHeader('TESTE DO ENDPOINT DE SAÚDE DO CACHE');
      const cacheHealthResponse = await axios.get(`${API_URL}/health/cache`, { headers });
      
      if (cacheHealthResponse.data.status === 'healthy') {
        logSuccess('Endpoint de saúde do cache: OK');
        logInfo(`Redis conectado: ${cacheHealthResponse.data.cache?.connected ? 'Sim' : 'Não'}`);
      } else {
        logWarning('Endpoint de saúde do cache reporta problemas');
      }
    } catch (error) {
      logWarning('Não foi possível acessar endpoint de saúde do cache');
    }
    
    logHeader('RESUMO DO TESTE DE API');
    logSuccess('Teste de cache em rotas reais concluído');
    logInfo('O cache está integrado e funcionando com a API');
    logInfo('Verifique os logs acima para detalhes de performance');
    
  } catch (error) {
    logError(`Erro durante o teste: ${error.message}`);
    
    if (error.message.includes('ECONNREFUSED')) {
      logError('API não está rodando. Inicie o servidor com: npm start');
    } else if (error.message.includes('autenticação')) {
      logError('Verifique as credenciais de teste no arquivo');
    }
  } finally {
    // Clean up
    try {
      await cacheService.close();
      logInfo('Conexão com cache fechada');
    } catch (error) {
      logWarning(`Erro ao fechar cache: ${error.message}`);
    }
  }
}

// Run test if this file is executed directly
if (require.main === module) {
  testApiCache().catch(console.error);
}

module.exports = { testApiCache };
