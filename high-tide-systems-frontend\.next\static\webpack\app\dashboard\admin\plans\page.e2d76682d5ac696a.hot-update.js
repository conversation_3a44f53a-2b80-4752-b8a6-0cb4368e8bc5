"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/plans/page",{

/***/ "(app-pages-browser)/./src/app/modules/admin/users/UsersPage.js":
/*!**************************************************!*\
  !*** ./src/app/modules/admin/users/UsersPage.js ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-cog.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ExportMenu */ \"(app-pages-browser)/./src/components/ui/ExportMenu.js\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/usePermissions */ \"(app-pages-browser)/./src/hooks/usePermissions.js\");\n/* harmony import */ var _components_permissions_Protected__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/permissions/Protected */ \"(app-pages-browser)/./src/components/permissions/Protected.js\");\n/* harmony import */ var _components_users_UserFormModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/users/UserFormModal */ \"(app-pages-browser)/./src/components/users/UserFormModal.js\");\n/* harmony import */ var _components_users_ModulesModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/users/ModulesModal */ \"(app-pages-browser)/./src/components/users/ModulesModal.js\");\n/* harmony import */ var _components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.js\");\n/* harmony import */ var _components_permissions_PermissionsModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/permissions/PermissionsModal */ \"(app-pages-browser)/./src/components/permissions/PermissionsModal.js\");\n/* harmony import */ var _components_users_RoleModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/users/RoleModal */ \"(app-pages-browser)/./src/components/users/RoleModal.js\");\n/* harmony import */ var _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/modules/admin/services/userService */ \"(app-pages-browser)/./src/app/modules/admin/services/userService.js\");\n/* harmony import */ var _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/modules/admin/services/companyService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyService.js\");\n/* harmony import */ var _services_subscriptionService__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/services/subscriptionService */ \"(app-pages-browser)/./src/services/subscriptionService.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/ModuleHeader */ \"(app-pages-browser)/./src/components/ui/ModuleHeader.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst UsersPage = ()=>{\n    var _subscriptionData_usage;\n    _s();\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { can } = (0,_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__.usePermissions)();\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [totalUsers, setTotalUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [moduleFilter, setModuleFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [companyFilter, setCompanyFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [usersFilter, setUsersFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [userOptions, setUserOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingUserOptions, setIsLoadingUserOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingCompanies, setIsLoadingCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userFormOpen, setUserFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedUser, setSelectedUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modulesModalOpen, setModulesModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [confirmationDialogOpen, setConfirmationDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [permissionsModalOpen, setPermissionsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [roleModalOpen, setRoleModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [actionToConfirm, setActionToConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [subscriptionData, setSubscriptionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingSubscription, setIsLoadingSubscription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Constantes\n    const ITEMS_PER_PAGE = 10;\n    const MODULE_LABELS = {\n        ADMIN: \"Administração\",\n        RH: \"RH\",\n        FINANCIAL: \"Financeiro\",\n        SCHEDULING: \"Agendamento\",\n        BASIC: \"Básico\"\n    };\n    // Verificar se o usuário atual é um system_admin ou company_admin\n    const isSystemAdmin = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"SYSTEM_ADMIN\";\n    const isAdmin = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"SYSTEM_ADMIN\" || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"COMPANY_ADMIN\";\n    // Verificar se pode adicionar usuários baseado no limite da subscription\n    const canAddUsers = (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData_usage = subscriptionData.usage) === null || _subscriptionData_usage === void 0 ? void 0 : _subscriptionData_usage.canAddUsers) !== false;\n    // Função para carregar empresas (apenas para system_admin)\n    const loadCompanies = async ()=>{\n        if (!isSystemAdmin) return;\n        setIsLoadingCompanies(true);\n        try {\n            const response = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_13__.companyService.getCompaniesForSelect();\n            setCompanies(response);\n        } catch (error) {\n            console.error(\"Erro ao carregar empresas:\", error);\n        } finally{\n            setIsLoadingCompanies(false);\n        }\n    };\n    // Função para carregar dados da subscription\n    const loadSubscriptionData = async ()=>{\n        if (isSystemAdmin) return; // System admin não tem limite de usuários\n        setIsLoadingSubscription(true);\n        try {\n            const response = await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_14__.subscriptionService.getSubscription();\n            setSubscriptionData(response);\n        } catch (error) {\n            console.error(\"Erro ao carregar dados da subscription:\", error);\n            // Se não conseguir carregar, assume que pode adicionar usuários\n            setSubscriptionData({\n                usage: {\n                    canAddUsers: true\n                }\n            });\n        } finally{\n            setIsLoadingSubscription(false);\n        }\n    };\n    // Função para carregar opções de usuários para o multi-select\n    const loadUserOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"UsersPage.useCallback[loadUserOptions]\": async ()=>{\n            setIsLoadingUserOptions(true);\n            try {\n                // Carregar todos os usuários para o multi-select (com limite maior)\n                const response = await _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_12__.userService.list(1, 100, {\n                    active: true // Apenas usuários ativos por padrão\n                });\n                // Transformar os dados para o formato esperado pelo MultiSelect\n                const options = response.users.map({\n                    \"UsersPage.useCallback[loadUserOptions].options\": (user)=>({\n                            value: user.id,\n                            label: user.fullName\n                        })\n                }[\"UsersPage.useCallback[loadUserOptions].options\"]);\n                setUserOptions(options);\n            } catch (error) {\n                console.error(\"Erro ao carregar opções de usuários:\", error);\n            } finally{\n                setIsLoadingUserOptions(false);\n            }\n        }\n    }[\"UsersPage.useCallback[loadUserOptions]\"], []);\n    const loadUsers = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : currentPage, searchQuery = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : search, status = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : statusFilter, module = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : moduleFilter, company = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : companyFilter, userIds = arguments.length > 5 && arguments[5] !== void 0 ? arguments[5] : usersFilter, sortField = arguments.length > 6 && arguments[6] !== void 0 ? arguments[6] : \"fullName\", sortDirection = arguments.length > 7 && arguments[7] !== void 0 ? arguments[7] : \"asc\";\n        setIsLoading(true);\n        try {\n            const filters = {\n                search: searchQuery || undefined,\n                active: status === \"\" ? undefined : status === \"active\",\n                module: module || undefined,\n                // Adiciona parâmetro para filtrar system_admin quando o usuário atual não for system_admin\n                excludeSystemAdmin: !isSystemAdmin,\n                // Adiciona parâmetro para filtrar por empresa (apenas para system_admin)\n                companyId: company || undefined,\n                // Adiciona parâmetro para filtrar por IDs específicos de usuários\n                userIds: userIds.length > 0 ? userIds : undefined,\n                // Adiciona parâmetros de ordenação\n                sortField: sortField,\n                sortDirection: sortDirection\n            };\n            const response = await _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_12__.userService.list(page, ITEMS_PER_PAGE, filters);\n            setUsers(response.users);\n            setTotalUsers(response.total);\n            setTotalPages(response.pages);\n            setCurrentPage(page);\n        } catch (error) {\n            console.error(\"Erro ao carregar usuários:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UsersPage.useEffect\": ()=>{\n            loadUsers();\n            // Carregar empresas se o usuário for system_admin\n            if (isSystemAdmin) {\n                loadCompanies();\n            }\n            // Carregar dados da subscription para verificar limite de usuários\n            loadSubscriptionData();\n            // Carregar opções de usuários para o multi-select\n            loadUserOptions();\n        }\n    }[\"UsersPage.useEffect\"], [\n        loadUserOptions\n    ]);\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        loadUsers(1, search, statusFilter, moduleFilter, companyFilter, usersFilter);\n    };\n    const handleStatusFilterChange = (value)=>{\n        setStatusFilter(value);\n        loadUsers(1, search, value, moduleFilter, companyFilter, usersFilter);\n    };\n    const handleModuleFilterChange = (value)=>{\n        setModuleFilter(value);\n        loadUsers(1, search, statusFilter, value, companyFilter, usersFilter);\n    };\n    const handleCompanyFilterChange = (value)=>{\n        setCompanyFilter(value);\n        loadUsers(1, search, statusFilter, moduleFilter, value, usersFilter);\n    };\n    const handleUsersFilterChange = (value)=>{\n        setUsersFilter(value);\n        loadUsers(1, search, statusFilter, moduleFilter, companyFilter, value);\n    };\n    const handlePageChange = (page)=>{\n        loadUsers(page, search, statusFilter, moduleFilter, companyFilter, usersFilter);\n    };\n    const handleResetFilters = ()=>{\n        setSearch(\"\");\n        setStatusFilter(\"\");\n        setModuleFilter(\"\");\n        setCompanyFilter(\"\");\n        setUsersFilter([]);\n        loadUsers(1, \"\", \"\", \"\", \"\", []);\n    };\n    const handleEditUser = (user)=>{\n        setSelectedUser(user);\n        setUserFormOpen(true);\n    };\n    const handleEditModules = (user)=>{\n        setSelectedUser(user);\n        setModulesModalOpen(true);\n    };\n    const handleManageRole = (user)=>{\n        setSelectedUser(user);\n        setRoleModalOpen(true);\n    };\n    const handleToggleStatus = (user)=>{\n        setSelectedUser(user);\n        setActionToConfirm({\n            type: \"toggle-status\",\n            message: \"\".concat(user.active ? \"Desativar\" : \"Ativar\", \" o usu\\xe1rio \").concat(user.fullName, \"?\")\n        });\n        setConfirmationDialogOpen(true);\n    };\n    const handleDeleteUser = (user)=>{\n        setSelectedUser(user);\n        setActionToConfirm({\n            type: \"delete\",\n            message: \"Excluir permanentemente o usu\\xe1rio \".concat(user.fullName, \"?\")\n        });\n        setConfirmationDialogOpen(true);\n    };\n    const handleManagePermissions = (user)=>{\n        setSelectedUser(user);\n        setPermissionsModalOpen(true);\n    };\n    const handleExport = async (format)=>{\n        setIsExporting(true);\n        try {\n            // Exportar usando os mesmos filtros da tabela atual\n            await _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_12__.userService.exportUsers({\n                search: search || undefined,\n                userIds: usersFilter.length > 0 ? usersFilter : undefined,\n                active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\n                module: moduleFilter || undefined,\n                companyId: companyFilter || undefined\n            }, format);\n        } catch (error) {\n            console.error(\"Erro ao exportar usuários:\", error);\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    const confirmAction = async ()=>{\n        if (actionToConfirm.type === \"toggle-status\") {\n            try {\n                await _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_12__.userService.toggleStatus(selectedUser.id, !selectedUser.active);\n                loadUsers();\n                loadSubscriptionData(); // Recarregar dados da subscription\n            } catch (error) {\n                console.error(\"Erro ao alterar status do usuário:\", error);\n            }\n        } else if (actionToConfirm.type === \"delete\") {\n            try {\n                await _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_12__.userService.delete(selectedUser.id);\n                loadUsers();\n                loadSubscriptionData(); // Recarregar dados da subscription\n            } catch (error) {\n                console.error(\"Erro ao excluir usuário:\", error);\n            }\n        }\n        setConfirmationDialogOpen(false);\n    };\n    // Import tutorial steps from tutorialMapping\n    const admUsersTutorialSteps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"UsersPage.useMemo[admUsersTutorialSteps]\": ()=>{\n            // Import dynamically to avoid circular dependencies\n            const tutorialMap = (__webpack_require__(/*! @/tutorials/tutorialMapping */ \"(app-pages-browser)/./src/tutorials/tutorialMapping.js\")[\"default\"]);\n            return tutorialMap['/dashboard/admin/users'] || [];\n        }\n    }[\"UsersPage.useMemo[admUsersTutorialSteps]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-slate-800 dark:text-white flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                size: 24,\n                                className: \"mr-2 text-slate-600 dark:text-slate-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Usu\\xe1rios\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                onExport: handleExport,\n                                isExporting: isExporting,\n                                disabled: isLoading || users.length === 0,\n                                className: \"text-slate-700 dark:text-slate-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, undefined),\n                            can(\"admin.users.create\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            if (canAddUsers) {\n                                                setSelectedUser(null);\n                                                setUserFormOpen(true);\n                                            }\n                                        },\n                                        disabled: !canAddUsers,\n                                        className: \"flex items-center gap-2 px-4 py-2 rounded-lg shadow-md transition-all \".concat(canAddUsers ? \"bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 text-white hover:from-slate-600 hover:to-slate-700 dark:hover:from-slate-700 dark:hover:to-slate-800\" : \"bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                size: 18\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 347,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Novo Usu\\xe1rio\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                        lineNumber: 333,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    !canAddUsers && (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.usage) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10\",\n                                        children: [\n                                            \"Limite de usu\\xe1rios atingido (\",\n                                            subscriptionData.usage.currentUsers,\n                                            \"/\",\n                                            subscriptionData.usage.userLimit,\n                                            \")\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-800\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 355,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                        lineNumber: 353,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                        lineNumber: 321,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"Filtros\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                    size: 22,\n                    className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                    lineNumber: 366,\n                    columnNumber: 15\n                }, void 0),\n                description: \"Gerencie os usu\\xe1rios do sistema. Utilize os filtros abaixo para encontrar usu\\xe1rios espec\\xedficos.\",\n                moduleColor: \"admin\",\n                tutorialSteps: admUsersTutorialSteps,\n                tutorialName: \"admin-users-overview\",\n                filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSearch,\n                    className: \"flex flex-col gap-4\",\n                    id: \"filtroUsuario\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 379,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Buscar por nome, email ou login...\",\n                                            value: search,\n                                            onChange: (e)=>setSearch(e.target.value),\n                                            className: \"w-full pl-10 pr-4 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 380,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 378,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full sm:w-40\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.ModuleSelect, {\n                                                moduleColor: \"admin\",\n                                                value: statusFilter,\n                                                onChange: (e)=>handleStatusFilterChange(e.target.value),\n                                                placeholder: \"Status\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Todos os status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"active\",\n                                                        children: \"Ativos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 398,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"inactive\",\n                                                        children: \"Inativos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 391,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 390,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full sm:w-48\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.ModuleSelect, {\n                                                moduleColor: \"admin\",\n                                                value: moduleFilter,\n                                                onChange: (e)=>handleModuleFilterChange(e.target.value),\n                                                placeholder: \"M\\xf3dulos\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Todos os m\\xf3dulos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"ADMIN\",\n                                                        children: \"Administra\\xe7\\xe3o\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"RH\",\n                                                        children: \"RH\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"FINANCIAL\",\n                                                        children: \"Financeiro\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"SCHEDULING\",\n                                                        children: \"Agendamento\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"BASIC\",\n                                                        children: \"B\\xe1sico\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 404,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 403,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full sm:w-48\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.ModuleSelect, {\n                                                moduleColor: \"admin\",\n                                                value: companyFilter,\n                                                onChange: (e)=>handleCompanyFilterChange(e.target.value),\n                                                placeholder: \"Empresa\",\n                                                disabled: isLoadingCompanies,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Todas as empresas\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: company.id,\n                                                            children: company.name\n                                                        }, company.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 25\n                                                        }, void 0))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 422,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 421,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_16__.FilterButton, {\n                                            type: \"submit\",\n                                            moduleColor: \"admin\",\n                                            variant: \"primary\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"sm:hidden\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 19\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden sm:inline\",\n                                                    children: \"Filtrar\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 19\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 439,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_16__.FilterButton, {\n                                            type: \"button\",\n                                            onClick: handleResetFilters,\n                                            moduleColor: \"admin\",\n                                            variant: \"secondary\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"sm:hidden\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 19\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden sm:inline\",\n                                                    children: \"Limpar\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 19\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 444,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 389,\n                                    columnNumber: 15\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                            lineNumber: 377,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.MultiSelect, {\n                                label: \"Filtrar por Usu\\xe1rios\",\n                                value: usersFilter,\n                                onChange: handleUsersFilterChange,\n                                options: userOptions,\n                                placeholder: \"Selecione um ou mais usu\\xe1rios pelo nome...\",\n                                loading: isLoadingUserOptions,\n                                moduleOverride: \"admin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 458,\n                                columnNumber: 15\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                            lineNumber: 457,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                    lineNumber: 372,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 364,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.ModuleTable, {\n                moduleColor: \"admin\",\n                columns: [\n                    {\n                        header: 'Usuário',\n                        field: 'fullName',\n                        width: '20%'\n                    },\n                    {\n                        header: 'Email',\n                        field: 'email',\n                        width: '15%'\n                    },\n                    {\n                        header: 'Profissão',\n                        field: 'profession',\n                        width: '15%'\n                    },\n                    {\n                        header: 'Módulos',\n                        field: 'modules',\n                        width: '10%'\n                    },\n                    {\n                        header: 'Função',\n                        field: 'role',\n                        width: '10%'\n                    },\n                    {\n                        header: 'Status',\n                        field: 'active',\n                        width: '8%'\n                    },\n                    {\n                        header: 'Ações',\n                        field: 'actions',\n                        className: 'text-right',\n                        width: '14%',\n                        sortable: false\n                    }\n                ],\n                data: users,\n                isLoading: isLoading,\n                emptyMessage: \"Nenhum usu\\xe1rio encontrado\",\n                emptyIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                    lineNumber: 487,\n                    columnNumber: 20\n                }, void 0),\n                currentPage: currentPage,\n                totalPages: totalPages,\n                totalItems: totalUsers,\n                onPageChange: handlePageChange,\n                showPagination: true,\n                tableId: \"admin-users-table\",\n                enableColumnToggle: true,\n                defaultSortField: \"fullName\",\n                defaultSortDirection: \"asc\",\n                onSort: (field, direction)=>{\n                    // Quando a ordenação mudar, recarregar os usuários com os novos parâmetros de ordenação\n                    loadUsers(currentPage, search, statusFilter, moduleFilter, companyFilter, usersFilter, field, direction);\n                },\n                renderRow: (user, index, moduleColors, visibleColumns)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                        className: moduleColors.hoverBg,\n                        children: [\n                            visibleColumns.includes('fullName') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-10 w-10 rounded-full bg-neutral-200 dark:bg-gray-600 flex items-center justify-center text-neutral-600 dark:text-neutral-300 font-medium flex-shrink-0 overflow-hidden\",\n                                            children: user.profileImageFullUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: user.profileImageFullUrl,\n                                                alt: user.fullName,\n                                                className: \"w-full h-full object-cover\",\n                                                onError: (e)=>{\n                                                    e.target.onerror = null;\n                                                    e.target.style.display = 'none';\n                                                    e.target.parentNode.innerHTML = user.fullName.charAt(0).toUpperCase();\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 517,\n                                                columnNumber: 23\n                                            }, void 0) : user.fullName.charAt(0).toUpperCase()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 515,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-neutral-800 dark:text-neutral-100 truncate\",\n                                                    children: user.fullName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 21\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-neutral-500 dark:text-neutral-400 truncate\",\n                                                    children: user.login\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 531,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 514,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 513,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('email') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300 truncate\",\n                                children: user.email\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 544,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('profession') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300\",\n                                children: user.professionObj ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            size: 14,\n                                            className: \"text-neutral-500 dark:text-neutral-400 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 553,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate\",\n                                            children: [\n                                                user.professionObj.name,\n                                                user.professionObj.group && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-neutral-400 dark:text-neutral-500 ml-1\",\n                                                    children: [\n                                                        \"(\",\n                                                        user.professionObj.group.name,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 554,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 552,\n                                    columnNumber: 19\n                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                    children: \"Sem profiss\\xe3o\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 564,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 550,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('modules') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-1\",\n                                    children: [\n                                        user.modules.slice(0, 2).map((module)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 text-xs rounded-full bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-neutral-300\",\n                                                children: MODULE_LABELS[module]\n                                            }, module, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 573,\n                                                columnNumber: 21\n                                            }, void 0)),\n                                        user.modules.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 text-xs rounded-full bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-neutral-300\",\n                                            children: [\n                                                \"+\",\n                                                user.modules.length - 2\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 581,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 571,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 570,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('role') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 text-xs rounded-full inline-flex items-center gap-1 \".concat(user.role === \"SYSTEM_ADMIN\" ? \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\" : user.role === \"COMPANY_ADMIN\" ? \"bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400\" : \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            size: 12,\n                                            className: \"flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 600,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate\",\n                                            children: user.role === \"SYSTEM_ADMIN\" ? \"Admin Sistema\" : user.role === \"COMPANY_ADMIN\" ? \"Admin Empresa\" : \"Funcionário\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 601,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 591,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 590,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('active') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit \".concat(user.active ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\" : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\"),\n                                    children: user.active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                size: 12,\n                                                className: \"flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 623,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Ativo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 624,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                size: 12,\n                                                className: \"flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 628,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Inativo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 629,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 614,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 613,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4 text-right text-sm font-medium\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_6__.Protected, {\n                                            permission: \"admin.users.edit\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleEditUser(user),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\",\n                                                id: \"edicaoUsuario\",\n                                                title: \"Editar\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 646,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 640,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 639,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_6__.Protected, {\n                                            permission: \"admin.permissions.manage\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleEditModules(user),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors\",\n                                                id: \"gerenciarModulo\",\n                                                title: \"Gerenciar m\\xf3dulos\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 657,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 651,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 650,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_6__.Protected, {\n                                            permission: \"admin.permissions.manage\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleManagePermissions(user),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-purple-500 dark:hover:text-purple-400 transition-colors\",\n                                                id: \"gerenciarPermissoes\",\n                                                title: \"Gerenciar permiss\\xf5es\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 668,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 662,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 661,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        (user.role !== \"SYSTEM_ADMIN\" || isSystemAdmin) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_6__.Protected, {\n                                            permission: \"admin.users.edit\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleManageRole(user),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-indigo-500 dark:hover:text-indigo-400 transition-colors\",\n                                                id: \"gerenciarFuncao\",\n                                                title: \"Alterar fun\\xe7\\xe3o\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 681,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 675,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 674,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        (user.role !== \"SYSTEM_ADMIN\" || isSystemAdmin) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_6__.Protected, {\n                                            permission: \"admin.users.edit\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleToggleStatus(user),\n                                                className: \"p-1 transition-colors \".concat(user.active ? \"text-neutral-500 dark:text-neutral-400 hover:text-amber-500 dark:hover:text-amber-400\" : \"text-neutral-500 dark:text-neutral-400 hover:text-green-500 dark:hover:text-green-400\"),\n                                                id: \"desativarUsuario\",\n                                                title: user.active ? \"Desativar\" : \"Ativar\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 699,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 689,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 688,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        (user.role !== \"SYSTEM_ADMIN\" || isSystemAdmin) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_6__.Protected, {\n                                            permission: \"admin.users.delete\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDeleteUser(user),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors\",\n                                                id: \"excluirUsuario\",\n                                                title: \"Excluir\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 713,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 707,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 706,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 638,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 637,\n                                columnNumber: 15\n                            }, void 0)\n                        ]\n                    }, user.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                        lineNumber: 511,\n                        columnNumber: 11\n                    }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 473,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_UserFormModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: userFormOpen,\n                onClose: ()=>setUserFormOpen(false),\n                user: selectedUser,\n                onSuccess: ()=>{\n                    setUserFormOpen(false);\n                    loadUsers();\n                },\n                currentUser: currentUser\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 725,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_ModulesModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: modulesModalOpen,\n                onClose: ()=>setModulesModalOpen(false),\n                user: selectedUser,\n                onSuccess: ()=>{\n                    setModulesModalOpen(false);\n                    loadUsers();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 736,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: confirmationDialogOpen,\n                onClose: ()=>setConfirmationDialogOpen(false),\n                onConfirm: confirmAction,\n                title: \"Confirmar a\\xe7\\xe3o\",\n                message: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.message) || \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 746,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_PermissionsModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: permissionsModalOpen,\n                onClose: ()=>setPermissionsModalOpen(false),\n                user: selectedUser,\n                onSuccess: ()=>{\n                    setPermissionsModalOpen(false);\n                    loadUsers();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 754,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_RoleModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: roleModalOpen,\n                onClose: ()=>setRoleModalOpen(false),\n                user: selectedUser,\n                onSuccess: ()=>{\n                    setRoleModalOpen(false);\n                    loadUsers();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 763,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n        lineNumber: 313,\n        columnNumber: 5\n    }, undefined);\n};\n_s(UsersPage, \"d8X6bUO97cMyw194FlS92cwYTas=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__.usePermissions\n    ];\n});\n_c = UsersPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UsersPage);\nvar _c;\n$RefreshReg$(_c, \"UsersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/admin/users/UsersPage.js\n"));

/***/ })

});