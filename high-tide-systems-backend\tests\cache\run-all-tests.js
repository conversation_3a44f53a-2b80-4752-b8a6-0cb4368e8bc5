// run-all-tests.js
require('dotenv').config();
const { runComprehensiveTests } = require('./comprehensive-cache-test');
const { runMiddlewareTests } = require('./middleware-test');
const { runPerformanceTests } = require('./performance-test');
const cacheService = require('../../src/services/cacheService');

// Test utilities
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
  underline: '\x1b[4m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logHeader(message) {
  log(`\n${colors.bold}${colors.underline}=== ${message} ===${colors.reset}`, 'cyan');
}

function logSubHeader(message) {
  log(`\n${colors.bold}--- ${message} ---${colors.reset}`, 'magenta');
}

// System information gathering
async function getSystemInfo() {
  const info = {
    node: process.version,
    platform: process.platform,
    arch: process.arch,
    memory: {
      total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
      used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024)
    },
    uptime: Math.round(process.uptime()),
    redis: {
      url: process.env.REDIS_URL || 'redis://localhost:6379',
      connected: false
    }
  };

  try {
    const initResult = await cacheService.initialize();
    info.redis.connected = initResult.success;
    
    if (initResult.success) {
      const stats = await cacheService.getStats();
      if (stats) {
        info.redis.version = stats.stats?.redis_version || 'unknown';
        info.redis.memory = stats.memory?.used_memory_human || 'unknown';
        info.redis.connections = stats.stats?.total_connections_received || 'unknown';
      }
    }
    
    await cacheService.close();
  } catch (error) {
    info.redis.error = error.message;
  }

  return info;
}

// Test environment validation
async function validateTestEnvironment() {
  logSubHeader('VALIDAÇÃO DO AMBIENTE DE TESTE');
  
  const issues = [];
  
  // Check Node.js version
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  if (majorVersion < 14) {
    issues.push(`Node.js version muito antiga: ${nodeVersion} (recomendado: 14+)`);
  } else {
    logSuccess(`Node.js version: ${nodeVersion}`);
  }
  
  // Check environment variables
  const requiredEnvVars = []; // REDIS_URL is optional, will use default
  const optionalEnvVars = ['NODE_ENV', 'DATABASE_URL', 'REDIS_URL'];

  requiredEnvVars.forEach(envVar => {
    if (!process.env[envVar]) {
      issues.push(`Variável de ambiente obrigatória não definida: ${envVar}`);
    } else {
      logSuccess(`${envVar}: ${process.env[envVar]}`);
    }
  });
  
  optionalEnvVars.forEach(envVar => {
    if (process.env[envVar]) {
      logInfo(`${envVar}: ${process.env[envVar]}`);
    } else {
      logWarning(`${envVar}: não definida (opcional)`);
    }
  });
  
  // Check Redis connectivity
  try {
    const initResult = await cacheService.initialize();
    if (initResult.success) {
      logSuccess('Conexão com Redis: OK');
      await cacheService.close();
    } else {
      issues.push(`Falha na conexão com Redis: ${initResult.error}`);
    }
  } catch (error) {
    issues.push(`Erro ao conectar com Redis: ${error.message}`);
  }
  
  // Check available memory (relaxed requirement for testing)
  const memoryUsage = process.memoryUsage();
  const availableMemory = memoryUsage.heapTotal / 1024 / 1024;
  if (availableMemory < 20) {
    issues.push(`Pouca memória disponível: ${availableMemory.toFixed(2)}MB`);
  } else {
    logSuccess(`Memória disponível: ${availableMemory.toFixed(2)}MB`);
  }
  
  if (issues.length > 0) {
    logError('Problemas encontrados no ambiente:');
    issues.forEach(issue => logError(`  - ${issue}`));
    return false;
  }
  
  logSuccess('Ambiente de teste validado com sucesso!');
  return true;
}

// Main test execution
async function runAllCacheTests() {
  const startTime = Date.now();
  
  logHeader('SUITE COMPLETA DE TESTES DO SISTEMA DE CACHE');
  logInfo('High Tide Systems - Cache Testing Suite');
  logInfo(`Iniciado em: ${new Date().toLocaleString()}`);
  
  // Display system information
  logSubHeader('INFORMAÇÕES DO SISTEMA');
  try {
    const systemInfo = await getSystemInfo();
    
    log(`Node.js: ${systemInfo.node}`, 'cyan');
    log(`Plataforma: ${systemInfo.platform} (${systemInfo.arch})`, 'cyan');
    log(`Memória: ${systemInfo.memory.used}MB / ${systemInfo.memory.total}MB`, 'cyan');
    log(`Uptime: ${systemInfo.uptime}s`, 'cyan');
    log(`Redis URL: ${systemInfo.redis.url}`, 'cyan');
    log(`Redis Conectado: ${systemInfo.redis.connected ? 'Sim' : 'Não'}`, 
        systemInfo.redis.connected ? 'green' : 'red');
    
    if (systemInfo.redis.version) {
      log(`Redis Version: ${systemInfo.redis.version}`, 'cyan');
    }
    if (systemInfo.redis.memory) {
      log(`Redis Memory: ${systemInfo.redis.memory}`, 'cyan');
    }
    if (systemInfo.redis.error) {
      logError(`Redis Error: ${systemInfo.redis.error}`);
    }
  } catch (error) {
    logError(`Erro ao obter informações do sistema: ${error.message}`);
  }
  
  // Validate test environment
  const environmentValid = await validateTestEnvironment();
  if (!environmentValid) {
    logError('Ambiente de teste inválido. Abortando testes.');
    process.exit(1);
  }
  
  const testResults = {
    comprehensive: { success: false, duration: 0, error: null },
    middleware: { success: false, duration: 0, error: null },
    performance: { success: false, duration: 0, error: null }
  };
  
  // Run comprehensive tests
  logHeader('1. TESTES ABRANGENTES DO CACHE');
  try {
    const testStart = Date.now();
    await runComprehensiveTests();
    testResults.comprehensive.duration = Date.now() - testStart;
    testResults.comprehensive.success = true;
    logSuccess(`Testes abrangentes concluídos em ${testResults.comprehensive.duration}ms`);
  } catch (error) {
    testResults.comprehensive.error = error.message;
    logError(`Testes abrangentes falharam: ${error.message}`);
  }
  
  // Wait between test suites
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Run middleware tests
  logHeader('2. TESTES DE MIDDLEWARE');
  try {
    const testStart = Date.now();
    await runMiddlewareTests();
    testResults.middleware.duration = Date.now() - testStart;
    testResults.middleware.success = true;
    logSuccess(`Testes de middleware concluídos em ${testResults.middleware.duration}ms`);
  } catch (error) {
    testResults.middleware.error = error.message;
    logError(`Testes de middleware falharam: ${error.message}`);
  }
  
  // Wait between test suites
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Run performance tests
  logHeader('3. TESTES DE PERFORMANCE');
  try {
    const testStart = Date.now();
    await runPerformanceTests();
    testResults.performance.duration = Date.now() - testStart;
    testResults.performance.success = true;
    logSuccess(`Testes de performance concluídos em ${testResults.performance.duration}ms`);
  } catch (error) {
    testResults.performance.error = error.message;
    logError(`Testes de performance falharam: ${error.message}`);
  }
  
  // Final results
  const totalDuration = Date.now() - startTime;
  
  logHeader('RESUMO FINAL DOS TESTES');
  
  const successCount = Object.values(testResults).filter(r => r.success).length;
  const totalTests = Object.keys(testResults).length;
  
  log(`\nResultados por categoria:`, 'bold');
  Object.entries(testResults).forEach(([category, result]) => {
    const status = result.success ? '✅ PASSOU' : '❌ FALHOU';
    const duration = result.duration > 0 ? ` (${result.duration}ms)` : '';
    const error = result.error ? ` - ${result.error}` : '';
    
    log(`  ${category.toUpperCase()}: ${status}${duration}${error}`, 
        result.success ? 'green' : 'red');
  });
  
  log(`\nResumo geral:`, 'bold');
  log(`  Total de categorias: ${totalTests}`, 'blue');
  log(`  Passou: ${successCount}`, 'green');
  log(`  Falhou: ${totalTests - successCount}`, successCount === totalTests ? 'green' : 'red');
  log(`  Taxa de sucesso: ${(successCount / totalTests * 100).toFixed(1)}%`, 
      successCount === totalTests ? 'green' : 'yellow');
  log(`  Tempo total: ${totalDuration}ms (${(totalDuration / 1000).toFixed(1)}s)`, 'cyan');
  
  if (successCount === totalTests) {
    logSuccess('\n🎉 TODOS OS TESTES PASSARAM! Sistema de cache funcionando perfeitamente.');
  } else {
    logError('\n⚠️  ALGUNS TESTES FALHARAM. Verifique os logs acima para detalhes.');
  }
  
  logInfo(`\nTestes finalizados em: ${new Date().toLocaleString()}`);
  
  // Exit with appropriate code
  process.exit(successCount === totalTests ? 0 : 1);
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logError(`Exceção não capturada: ${error.message}`);
  console.error(error.stack);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logError(`Promise rejeitada não tratada: ${reason}`);
  console.error(reason);
  process.exit(1);
});

// Run tests if this file is executed directly
if (require.main === module) {
  runAllCacheTests().catch((error) => {
    logError(`Erro fatal durante os testes: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  });
}

module.exports = { runAllCacheTests };
