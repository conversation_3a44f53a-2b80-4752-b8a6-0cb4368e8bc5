# Testes de Cache Redis

Esta pasta contém uma suite completa de testes para a implementação do Redis Cache na aplicação High Tide Systems.

## Arquivos de Teste

### Testes Principais
- **final-cache-test.js**: 🚀 **RECOMENDADO** - Teste final abrangente e limpo
- **quick-test.js**: Teste rápido para verificação básica do sistema
- **api-cache-test.js**: Teste cache em rotas reais da API (requer servidor rodando)
- **run-all-tests.js**: Suite completa de todos os testes (pode ter warnings)
- **comprehensive-cache-test.js**: Testes abrangentes do serviço de cache
- **middleware-test.js**: Testes específicos dos middlewares de cache
- **performance-test.js**: Testes de performance e benchmarks

### Testes Legados (mantidos para compatibilidade)
- **redis-basic.js**: Teste básico da conexão e operações do Redis
- **redis-simple.js**: Teste simplificado do Redis para verificação rápida
- **middleware.js**: Teste do middleware de cache para rotas
- **cache-simple.js**: Teste simplificado do serviço de cache
- **test-redis-container.js**: Teste direto do Redis no container
- **test-middleware.js**: Teste do middleware de cache
- **test-cache-service.js**: Teste do serviço de cache

## Como Executar os Testes

### Pré-requisitos
1. Redis rodando (via Docker Compose)
2. Dependências instaladas
3. Variáveis de ambiente configuradas

### Execução Rápida
```bash
# Teste rápido (recomendado para verificação inicial)
node tests/cache/quick-test.js

# Teste final abrangente (recomendado)
node tests/cache/final-cache-test.js
```

### Suite Completa
```bash
# Todos os testes (validação completa - pode ter alguns warnings)
node tests/cache/run-all-tests.js
```

### Teste com API Real
```bash
# Teste cache em rotas reais (requer API rodando)
node tests/cache/api-cache-test.js
```

### Testes Individuais
```bash
# Testes abrangentes do cache
node tests/cache/comprehensive-cache-test.js

# Testes de middleware
node tests/cache/middleware-test.js

# Testes de performance
node tests/cache/performance-test.js
```

### Dentro do Container Docker
```bash
# Entrar no container
docker exec -it high-tide-systems-api bash

# Executar testes
node tests/cache/run-all-tests.js
```

## Tipos de Teste

### 1. Testes Funcionais
- ✅ Conexão com Redis
- ✅ Operações básicas (SET, GET, DELETE)
- ✅ Geração de chaves
- ✅ TTL (Time To Live)
- ✅ Limpeza por padrão
- ✅ Compressão de dados
- ✅ Função getOrSet
- ✅ Estatísticas do cache

### 2. Testes de Middleware
- ✅ Cache middleware básico
- ✅ Advanced cache middleware
- ✅ Estratégias de cache (standard, session, search, reference)
- ✅ Clear cache middleware
- ✅ Headers de cache
- ✅ Cache hit/miss

### 3. Testes de Performance
- ✅ Operações básicas (SET/GET)
- ✅ Diferentes tamanhos de dados
- ✅ Operações concorrentes
- ✅ Performance de compressão
- ✅ Limpeza por padrão em massa
- ✅ Benchmarks de getOrSet

## Interpretação dos Resultados

### Indicadores de Sucesso
- ✅ **Verde**: Teste passou
- ❌ **Vermelho**: Teste falhou
- ⚠️ **Amarelo**: Aviso ou performance subótima
- ℹ️ **Azul**: Informação

### Métricas de Performance
- **< 10ms**: Excelente
- **10-50ms**: Bom
- **50-100ms**: Aceitável
- **> 100ms**: Pode precisar de otimização

### Taxa de Sucesso Esperada
- **100%**: Sistema funcionando perfeitamente
- **90-99%**: Alguns problemas menores
- **< 90%**: Problemas significativos que precisam ser investigados

## Notas Importantes

- Os testes devem ser executados dentro do container Docker para ter acesso ao Redis
- Alguns testes podem exigir autenticação, verifique o código antes de executar
- Os testes são independentes e podem ser executados em qualquer ordem
