// comprehensive-cache-test.js
require('dotenv').config();
const cacheService = require('../../src/services/cacheService');
const AdvancedCacheMiddleware = require('../../src/middlewares/advancedCache');
const { cacheMiddleware, clearCacheMiddleware } = require('../../src/middlewares/cache');
const externalApiCacheService = require('../../src/services/externalApiCacheService');

// Test utilities
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logHeader(message) {
  log(`\n${colors.bold}=== ${message} ===${colors.reset}`, 'blue');
}

// Performance measurement utility
function measureTime(fn) {
  return async (...args) => {
    const start = process.hrtime.bigint();
    const result = await fn(...args);
    const end = process.hrtime.bigint();
    const duration = Number(end - start) / 1000000; // Convert to milliseconds
    return { result, duration };
  };
}

// Mock Express objects for middleware testing
function createMockExpressObjects(options = {}) {
  const req = {
    method: 'GET',
    originalUrl: '/api/test',
    query: {},
    user: {
      id: 'test-user-123',
      companyId: 'test-company-456',
      role: 'admin'
    },
    ...options.req
  };

  const res = {
    statusCode: 200,
    json: jest.fn(),
    send: jest.fn(),
    set: jest.fn(),
    ...options.res
  };

  const next = jest.fn();

  return { req, res, next };
}

// Create jest mock
global.jest = {
  fn: () => {
    const mockFn = (...args) => {
      mockFn.mock.calls.push(args);
      return mockFn.mock.returnValue;
    };
    mockFn.mock = { 
      calls: [],
      returnValue: undefined
    };
    return mockFn;
  }
};

class CacheTestSuite {
  constructor() {
    this.testResults = {
      passed: 0,
      failed: 0,
      total: 0
    };
  }

  async runTest(testName, testFn) {
    this.testResults.total++;
    try {
      logInfo(`Executando: ${testName}`);
      await testFn();
      this.testResults.passed++;
      logSuccess(`${testName} - PASSOU`);
    } catch (error) {
      this.testResults.failed++;
      logError(`${testName} - FALHOU: ${error.message}`);
      console.error(error.stack);
    }
  }

  printResults() {
    logHeader('RESULTADOS DOS TESTES');
    log(`Total de testes: ${this.testResults.total}`, 'blue');
    log(`Passou: ${this.testResults.passed}`, 'green');
    log(`Falhou: ${this.testResults.failed}`, this.testResults.failed > 0 ? 'red' : 'green');
    
    const successRate = (this.testResults.passed / this.testResults.total * 100).toFixed(1);
    log(`Taxa de sucesso: ${successRate}%`, successRate === '100.0' ? 'green' : 'yellow');
  }

  // Test 1: Basic Cache Service Operations
  async testBasicCacheOperations() {
    const testKey = 'test:basic:operations';
    const testValue = { 
      message: 'Teste básico', 
      timestamp: new Date().toISOString(),
      data: Array.from({length: 100}, (_, i) => ({ id: i, value: `item-${i}` }))
    };

    // Test SET operation
    const setResult = await cacheService.set(testKey, testValue, 60);
    if (!setResult) throw new Error('Falha ao armazenar valor no cache');

    // Test GET operation
    const getValue = await cacheService.get(testKey);
    if (!getValue) throw new Error('Falha ao recuperar valor do cache');
    if (JSON.stringify(getValue) !== JSON.stringify(testValue)) {
      throw new Error('Valor recuperado não confere com o valor armazenado');
    }

    // Test TTL
    const ttl = await cacheService.getTTL(testKey);
    if (ttl <= 0 || ttl > 60) throw new Error(`TTL inválido: ${ttl}`);

    // Test DELETE operation
    const deleteResult = await cacheService.delete(testKey);
    if (!deleteResult) throw new Error('Falha ao deletar valor do cache');

    // Verify deletion
    const deletedValue = await cacheService.get(testKey);
    if (deletedValue !== null) throw new Error('Valor não foi deletado corretamente');
  }

  // Test 2: Cache Key Generation
  async testKeyGeneration() {
    const prefix = 'users';
    const params = { 
      userId: 123, 
      role: 'admin', 
      filter: 'active',
      page: 1,
      limit: 10
    };

    const generatedKey = cacheService.generateKey(prefix, params);
    
    if (!generatedKey.startsWith(prefix)) {
      throw new Error(`Chave não inicia com o prefixo correto: ${generatedKey}`);
    }
    
    if (!generatedKey.includes('userId:123')) {
      throw new Error(`Chave não contém userId: ${generatedKey}`);
    }
    
    if (!generatedKey.includes('role:admin')) {
      throw new Error(`Chave não contém role: ${generatedKey}`);
    }

    logInfo(`Chave gerada: ${generatedKey}`);
  }

  // Test 3: Pattern-based Cache Clearing
  async testPatternClearing() {
    const pattern = 'test:pattern';
    const keys = [];
    
    // Create multiple keys with the same pattern
    for (let i = 1; i <= 5; i++) {
      const key = `${pattern}:${i}`;
      keys.push(key);
      await cacheService.set(key, { index: i, data: `test-${i}` }, 60);
    }

    // Verify all keys exist
    for (const key of keys) {
      const value = await cacheService.get(key);
      if (!value) throw new Error(`Chave ${key} não foi criada corretamente`);
    }

    // Clear all keys with pattern
    const clearResult = await cacheService.clear(`${pattern}:*`);
    if (!clearResult) throw new Error('Falha ao limpar cache por padrão');

    // Verify all keys are deleted
    for (const key of keys) {
      const value = await cacheService.get(key);
      if (value !== null) throw new Error(`Chave ${key} não foi deletada`);
    }
  }

  // Test 4: Cache Compression
  async testCacheCompression() {
    const testKey = 'test:compression';
    const largeData = {
      message: 'Teste de compressão',
      data: Array.from({length: 1000}, (_, i) => ({
        id: i,
        name: `Item ${i}`,
        description: `Descrição detalhada do item ${i} com muito texto para testar compressão`,
        metadata: {
          created: new Date().toISOString(),
          tags: [`tag-${i}`, `category-${i % 10}`, `type-${i % 5}`],
          properties: Object.fromEntries(
            Array.from({length: 10}, (_, j) => [`prop${j}`, `value${j}-${i}`])
          )
        }
      }))
    };

    // Test compressed storage
    const setResult = await cacheService.setCompressed(testKey, largeData, 60, true);
    if (!setResult) throw new Error('Falha ao armazenar dados comprimidos');

    // Test compressed retrieval
    const getValue = await cacheService.getCompressed(testKey);
    if (!getValue) throw new Error('Falha ao recuperar dados comprimidos');
    
    if (getValue.data.length !== largeData.data.length) {
      throw new Error('Dados comprimidos não foram recuperados corretamente');
    }

    // Clean up
    await cacheService.delete(testKey);
    await cacheService.delete(`${testKey}:compressed`);
  }

  // Test 5: getOrSet functionality
  async testGetOrSet() {
    const testKey = 'test:getOrSet';
    let callCount = 0;
    
    const fallbackFunction = async () => {
      callCount++;
      return {
        message: 'Dados do fallback',
        timestamp: new Date().toISOString(),
        callNumber: callCount
      };
    };

    // First call should execute fallback
    const firstResult = await cacheService.getOrSet(testKey, fallbackFunction, 60);
    if (callCount !== 1) throw new Error('Função fallback não foi chamada na primeira vez');
    if (!firstResult.message) throw new Error('Resultado do fallback inválido');

    // Second call should use cache
    const secondResult = await cacheService.getOrSet(testKey, fallbackFunction, 60);
    if (callCount !== 1) throw new Error('Função fallback foi chamada novamente (deveria usar cache)');
    if (secondResult.callNumber !== 1) throw new Error('Cache não foi utilizado corretamente');

    // Clean up
    await cacheService.delete(testKey);
  }
}

// Main test execution
async function runComprehensiveTests() {
  logHeader('INICIANDO TESTES ABRANGENTES DO SISTEMA DE CACHE');
  
  const testSuite = new CacheTestSuite();

  try {
    // Initialize cache service
    logInfo('Inicializando serviço de cache...');
    const initResult = await cacheService.initialize();
    if (!initResult.success) {
      throw new Error(`Falha ao inicializar cache: ${initResult.error}`);
    }
    logSuccess('Serviço de cache inicializado com sucesso');

    // Run all tests
    await testSuite.runTest('Operações Básicas de Cache', () => testSuite.testBasicCacheOperations());
    await testSuite.runTest('Geração de Chaves', () => testSuite.testKeyGeneration());
    await testSuite.runTest('Limpeza por Padrão', () => testSuite.testPatternClearing());
    await testSuite.runTest('Compressão de Cache', () => testSuite.testCacheCompression());
    await testSuite.runTest('Funcionalidade getOrSet', () => testSuite.testGetOrSet());

  } catch (error) {
    logError(`Erro durante a inicialização: ${error.message}`);
  } finally {
    // Print results
    testSuite.printResults();
    
    // Close connection
    try {
      await cacheService.close();
      logInfo('Conexão com cache fechada');
    } catch (error) {
      logWarning(`Erro ao fechar conexão: ${error.message}`);
    }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runComprehensiveTests().catch(console.error);
}

module.exports = { CacheTestSuite, runComprehensiveTests };
