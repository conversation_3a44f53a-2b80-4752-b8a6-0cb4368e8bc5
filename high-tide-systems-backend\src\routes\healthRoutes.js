// src/routes/healthRoutes.js
const express = require('express');
const healthCheckService = require('../services/healthCheckService');
const loggerService = require('../services/loggerService');
const cacheService = require('../services/cacheService');
const externalApiCacheService = require('../services/externalApiCacheService');

const router = express.Router();

/**
 * Health check completo - para monitoramento detalhado
 * GET /health
 */
router.get('/', async (req, res) => {
  try {
    const healthStatus = await healthCheckService.runAllChecks();
    
    // Status HTTP baseado no resultado
    const statusCode = healthStatus.status === 'healthy' ? 200 : 503;
    
    res.status(statusCode).json(healthStatus);
  } catch (error) {
    loggerService.error('HEALTH_CHECK_ERROR', {
      error: error.message,
      stack: error.stack
    });

    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
      message: error.message
    });
  }
});

/**
 * Health check simples - para load balancer
 * GET /health/simple
 */
router.get('/simple', async (req, res) => {
  try {
    const status = await healthCheckService.getSimpleStatus();
    const statusCode = status.status === 'healthy' ? 200 : 503;
    
    res.status(statusCode).json(status);
  } catch (error) {
    res.status(503).json({ status: 'unhealthy' });
  }
});

/**
 * Health check específico
 * GET /health/:checkName
 */
router.get('/:checkName', async (req, res) => {
  try {
    const { checkName } = req.params;
    const result = await healthCheckService.runCheck(checkName);
    
    const statusCode = result.status === 'healthy' ? 200 : 503;
    res.status(statusCode).json(result);
  } catch (error) {
    res.status(404).json({
      status: 'error',
      message: error.message
    });
  }
});

/**
 * Readiness check - verifica se a aplicação está pronta para receber tráfego
 * GET /health/readiness
 */
router.get('/readiness', async (req, res) => {
  try {
    // Verificar apenas componentes críticos para inicialização
    const criticalChecks = await Promise.all([
      healthCheckService.runCheck('database'),
      healthCheckService.runCheck('redis')
    ]);

    const allHealthy = criticalChecks.every(check => check.status === 'healthy');
    
    if (allHealthy) {
      res.status(200).json({
        status: 'ready',
        timestamp: new Date().toISOString(),
        checks: criticalChecks
      });
    } else {
      res.status(503).json({
        status: 'not_ready',
        timestamp: new Date().toISOString(),
        checks: criticalChecks
      });
    }
  } catch (error) {
    res.status(503).json({
      status: 'not_ready',
      error: error.message
    });
  }
});

/**
 * Liveness check - verifica se a aplicação está viva
 * GET /health/liveness
 */
router.get('/liveness', (req, res) => {
  // Check básico - se chegou até aqui, a aplicação está viva
  res.status(200).json({
    status: 'alive',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    pid: process.pid
  });
});

/**
 * Cache statistics - estatísticas detalhadas do cache
 * GET /health/cache
 */
router.get('/cache', async (req, res) => {
  try {
    const cacheStats = await cacheService.getStats();
    const apiStats = await externalApiCacheService.getApiStats();

    res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      cache: {
        redis: cacheStats,
        apis: apiStats,
        connected: cacheService.connected
      }
    });
  } catch (error) {
    loggerService.error('CACHE_STATS_ERROR', {
      error: error.message,
      stack: error.stack
    });

    res.status(500).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: 'Erro ao obter estatísticas do cache',
      message: error.message
    });
  }
});

/**
 * Cache management - limpar cache específico
 * DELETE /health/cache/:pattern
 */
router.delete('/cache/:pattern', async (req, res) => {
  try {
    const { pattern } = req.params;
    const { confirm } = req.query;

    if (confirm !== 'true') {
      return res.status(400).json({
        error: 'Confirmação necessária',
        message: 'Adicione ?confirm=true para confirmar a limpeza do cache'
      });
    }

    const result = await cacheService.clear(pattern);

    loggerService.info('CACHE_MANUAL_CLEAR', {
      pattern,
      success: result,
      user: req.user?.id
    });

    res.status(200).json({
      status: 'success',
      message: `Cache limpo para padrão: ${pattern}`,
      pattern,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    loggerService.error('CACHE_CLEAR_ERROR', {
      error: error.message,
      pattern: req.params.pattern
    });

    res.status(500).json({
      status: 'error',
      error: 'Erro ao limpar cache',
      message: error.message
    });
  }
});

module.exports = router;
