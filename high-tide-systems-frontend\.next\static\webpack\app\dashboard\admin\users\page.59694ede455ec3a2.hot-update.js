"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/users/page",{

/***/ "(app-pages-browser)/./src/app/modules/admin/users/UsersPage.js":
/*!**************************************************!*\
  !*** ./src/app/modules/admin/users/UsersPage.js ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-cog.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ExportMenu */ \"(app-pages-browser)/./src/components/ui/ExportMenu.js\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/usePermissions */ \"(app-pages-browser)/./src/hooks/usePermissions.js\");\n/* harmony import */ var _components_permissions_Protected__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/permissions/Protected */ \"(app-pages-browser)/./src/components/permissions/Protected.js\");\n/* harmony import */ var _components_users_UserFormModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/users/UserFormModal */ \"(app-pages-browser)/./src/components/users/UserFormModal.js\");\n/* harmony import */ var _components_users_ModulesModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/users/ModulesModal */ \"(app-pages-browser)/./src/components/users/ModulesModal.js\");\n/* harmony import */ var _components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.js\");\n/* harmony import */ var _components_permissions_PermissionsModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/permissions/PermissionsModal */ \"(app-pages-browser)/./src/components/permissions/PermissionsModal.js\");\n/* harmony import */ var _components_users_RoleModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/users/RoleModal */ \"(app-pages-browser)/./src/components/users/RoleModal.js\");\n/* harmony import */ var _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/modules/admin/services/userService */ \"(app-pages-browser)/./src/app/modules/admin/services/userService.js\");\n/* harmony import */ var _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/modules/admin/services/companyService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyService.js\");\n/* harmony import */ var _services_subscriptionService__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/services/subscriptionService */ \"(app-pages-browser)/./src/services/subscriptionService.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/ModuleHeader */ \"(app-pages-browser)/./src/components/ui/ModuleHeader.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst UsersPage = ()=>{\n    var _subscriptionData_usage;\n    _s();\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { can } = (0,_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__.usePermissions)();\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [totalUsers, setTotalUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [moduleFilter, setModuleFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [companyFilter, setCompanyFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [usersFilter, setUsersFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [userOptions, setUserOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingUserOptions, setIsLoadingUserOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingCompanies, setIsLoadingCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userFormOpen, setUserFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedUser, setSelectedUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modulesModalOpen, setModulesModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [confirmationDialogOpen, setConfirmationDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [permissionsModalOpen, setPermissionsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [roleModalOpen, setRoleModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [actionToConfirm, setActionToConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [subscriptionData, setSubscriptionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingSubscription, setIsLoadingSubscription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Constantes\n    const ITEMS_PER_PAGE = 10;\n    const MODULE_LABELS = {\n        ADMIN: \"Administração\",\n        RH: \"RH\",\n        FINANCIAL: \"Financeiro\",\n        SCHEDULING: \"Agendamento\",\n        BASIC: \"Básico\"\n    };\n    // Verificar se o usuário atual é um system_admin ou company_admin\n    const isSystemAdmin = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"SYSTEM_ADMIN\";\n    const isAdmin = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"SYSTEM_ADMIN\" || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"COMPANY_ADMIN\";\n    // Verificar se pode adicionar usuários baseado no limite da subscription\n    const canAddUsers = (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData_usage = subscriptionData.usage) === null || _subscriptionData_usage === void 0 ? void 0 : _subscriptionData_usage.canAddUsers) !== false;\n    // Função para carregar empresas (apenas para system_admin)\n    const loadCompanies = async ()=>{\n        if (!isSystemAdmin) return;\n        setIsLoadingCompanies(true);\n        try {\n            const response = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_13__.companyService.getCompaniesForSelect();\n            setCompanies(response);\n        } catch (error) {\n            console.error(\"Erro ao carregar empresas:\", error);\n        } finally{\n            setIsLoadingCompanies(false);\n        }\n    };\n    // Função para carregar dados da subscription\n    const loadSubscriptionData = async ()=>{\n        if (isSystemAdmin) return; // System admin não tem limite de usuários\n        setIsLoadingSubscription(true);\n        try {\n            const response = await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_14__.subscriptionService.getSubscription();\n            setSubscriptionData(response);\n        } catch (error) {\n            console.error(\"Erro ao carregar dados da subscription:\", error);\n            // Se não conseguir carregar, assume que pode adicionar usuários\n            setSubscriptionData({\n                usage: {\n                    canAddUsers: true\n                }\n            });\n        } finally{\n            setIsLoadingSubscription(false);\n        }\n    };\n    // Função para carregar opções de usuários para o multi-select\n    const loadUserOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"UsersPage.useCallback[loadUserOptions]\": async ()=>{\n            setIsLoadingUserOptions(true);\n            try {\n                // Carregar todos os usuários para o multi-select (com limite maior)\n                const response = await _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_12__.userService.list(1, 100, {\n                    active: true // Apenas usuários ativos por padrão\n                });\n                // Transformar os dados para o formato esperado pelo MultiSelect\n                const options = response.users.map({\n                    \"UsersPage.useCallback[loadUserOptions].options\": (user)=>({\n                            value: user.id,\n                            label: user.fullName\n                        })\n                }[\"UsersPage.useCallback[loadUserOptions].options\"]);\n                setUserOptions(options);\n            } catch (error) {\n                console.error(\"Erro ao carregar opções de usuários:\", error);\n            } finally{\n                setIsLoadingUserOptions(false);\n            }\n        }\n    }[\"UsersPage.useCallback[loadUserOptions]\"], []);\n    const loadUsers = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : currentPage, searchQuery = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : search, status = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : statusFilter, module = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : moduleFilter, company = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : companyFilter, userIds = arguments.length > 5 && arguments[5] !== void 0 ? arguments[5] : usersFilter, sortField = arguments.length > 6 && arguments[6] !== void 0 ? arguments[6] : \"fullName\", sortDirection = arguments.length > 7 && arguments[7] !== void 0 ? arguments[7] : \"asc\";\n        setIsLoading(true);\n        try {\n            const filters = {\n                search: searchQuery || undefined,\n                active: status === \"\" ? undefined : status === \"active\",\n                module: module || undefined,\n                // Adiciona parâmetro para filtrar system_admin quando o usuário atual não for system_admin\n                excludeSystemAdmin: !isSystemAdmin,\n                // Adiciona parâmetro para filtrar por empresa (apenas para system_admin)\n                companyId: company || undefined,\n                // Adiciona parâmetro para filtrar por IDs específicos de usuários\n                userIds: userIds.length > 0 ? userIds : undefined,\n                // Adiciona parâmetros de ordenação\n                sortField: sortField,\n                sortDirection: sortDirection\n            };\n            const response = await _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_12__.userService.list(page, ITEMS_PER_PAGE, filters);\n            setUsers(response.users);\n            setTotalUsers(response.total);\n            setTotalPages(response.pages);\n            setCurrentPage(page);\n        } catch (error) {\n            console.error(\"Erro ao carregar usuários:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UsersPage.useEffect\": ()=>{\n            loadUsers();\n            // Carregar empresas se o usuário for system_admin\n            if (isSystemAdmin) {\n                loadCompanies();\n            }\n            // Carregar dados da subscription para verificar limite de usuários\n            loadSubscriptionData();\n            // Carregar opções de usuários para o multi-select\n            loadUserOptions();\n        }\n    }[\"UsersPage.useEffect\"], [\n        loadUserOptions\n    ]);\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        loadUsers(1, search, statusFilter, moduleFilter, companyFilter, usersFilter);\n    };\n    const handleStatusFilterChange = (value)=>{\n        setStatusFilter(value);\n        loadUsers(1, search, value, moduleFilter, companyFilter, usersFilter);\n    };\n    const handleModuleFilterChange = (value)=>{\n        setModuleFilter(value);\n        loadUsers(1, search, statusFilter, value, companyFilter, usersFilter);\n    };\n    const handleCompanyFilterChange = (value)=>{\n        setCompanyFilter(value);\n        loadUsers(1, search, statusFilter, moduleFilter, value, usersFilter);\n    };\n    const handleUsersFilterChange = (value)=>{\n        setUsersFilter(value);\n        loadUsers(1, search, statusFilter, moduleFilter, companyFilter, value);\n    };\n    const handlePageChange = (page)=>{\n        loadUsers(page, search, statusFilter, moduleFilter, companyFilter, usersFilter);\n    };\n    const handleResetFilters = ()=>{\n        setSearch(\"\");\n        setStatusFilter(\"\");\n        setModuleFilter(\"\");\n        setCompanyFilter(\"\");\n        setUsersFilter([]);\n        loadUsers(1, \"\", \"\", \"\", \"\", []);\n    };\n    const handleEditUser = (user)=>{\n        setSelectedUser(user);\n        setUserFormOpen(true);\n    };\n    const handleEditModules = (user)=>{\n        setSelectedUser(user);\n        setModulesModalOpen(true);\n    };\n    const handleManageRole = (user)=>{\n        setSelectedUser(user);\n        setRoleModalOpen(true);\n    };\n    const handleToggleStatus = (user)=>{\n        setSelectedUser(user);\n        setActionToConfirm({\n            type: \"toggle-status\",\n            message: \"\".concat(user.active ? \"Desativar\" : \"Ativar\", \" o usu\\xe1rio \").concat(user.fullName, \"?\")\n        });\n        setConfirmationDialogOpen(true);\n    };\n    const handleDeleteUser = (user)=>{\n        setSelectedUser(user);\n        setActionToConfirm({\n            type: \"delete\",\n            message: \"Excluir permanentemente o usu\\xe1rio \".concat(user.fullName, \"?\")\n        });\n        setConfirmationDialogOpen(true);\n    };\n    const handleManagePermissions = (user)=>{\n        setSelectedUser(user);\n        setPermissionsModalOpen(true);\n    };\n    const handleExport = async (format)=>{\n        setIsExporting(true);\n        try {\n            // Exportar usando os mesmos filtros da tabela atual\n            await _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_12__.userService.exportUsers({\n                search: search || undefined,\n                userIds: usersFilter.length > 0 ? usersFilter : undefined,\n                active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\n                module: moduleFilter || undefined,\n                companyId: companyFilter || undefined\n            }, format);\n        } catch (error) {\n            console.error(\"Erro ao exportar usuários:\", error);\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    const confirmAction = async ()=>{\n        if (actionToConfirm.type === \"toggle-status\") {\n            try {\n                await _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_12__.userService.toggleStatus(selectedUser.id, !selectedUser.active);\n                loadUsers();\n            } catch (error) {\n                console.error(\"Erro ao alterar status do usuário:\", error);\n            }\n        } else if (actionToConfirm.type === \"delete\") {\n            try {\n                await _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_12__.userService.delete(selectedUser.id);\n                loadUsers();\n            } catch (error) {\n                console.error(\"Erro ao excluir usuário:\", error);\n            }\n        }\n        setConfirmationDialogOpen(false);\n    };\n    // Import tutorial steps from tutorialMapping\n    const admUsersTutorialSteps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"UsersPage.useMemo[admUsersTutorialSteps]\": ()=>{\n            // Import dynamically to avoid circular dependencies\n            const tutorialMap = (__webpack_require__(/*! @/tutorials/tutorialMapping */ \"(app-pages-browser)/./src/tutorials/tutorialMapping.js\")[\"default\"]);\n            return tutorialMap['/dashboard/admin/users'] || [];\n        }\n    }[\"UsersPage.useMemo[admUsersTutorialSteps]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-slate-800 dark:text-white flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                size: 24,\n                                className: \"mr-2 text-slate-600 dark:text-slate-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 315,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Usu\\xe1rios\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                        lineNumber: 314,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                onExport: handleExport,\n                                isExporting: isExporting,\n                                disabled: isLoading || users.length === 0,\n                                className: \"text-slate-700 dark:text-slate-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 321,\n                                columnNumber: 11\n                            }, undefined),\n                            can(\"admin.users.create\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setSelectedUser(null);\n                                    setUserFormOpen(true);\n                                },\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 text-white rounded-lg hover:from-slate-600 hover:to-slate-700 dark:hover:from-slate-700 dark:hover:to-slate-800 shadow-md transition-all\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Novo Usu\\xe1rio\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                        lineNumber: 338,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 313,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"Filtros\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                    size: 22,\n                    className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                    lineNumber: 347,\n                    columnNumber: 15\n                }, void 0),\n                description: \"Gerencie os usu\\xe1rios do sistema. Utilize os filtros abaixo para encontrar usu\\xe1rios espec\\xedficos.\",\n                moduleColor: \"admin\",\n                tutorialSteps: admUsersTutorialSteps,\n                tutorialName: \"admin-users-overview\",\n                filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSearch,\n                    className: \"flex flex-col gap-4\",\n                    id: \"filtroUsuario\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 360,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Buscar por nome, email ou login...\",\n                                            value: search,\n                                            onChange: (e)=>setSearch(e.target.value),\n                                            className: \"w-full pl-10 pr-4 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 361,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 359,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full sm:w-40\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.ModuleSelect, {\n                                                moduleColor: \"admin\",\n                                                value: statusFilter,\n                                                onChange: (e)=>handleStatusFilterChange(e.target.value),\n                                                placeholder: \"Status\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Todos os status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"active\",\n                                                        children: \"Ativos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"inactive\",\n                                                        children: \"Inativos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 372,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 371,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full sm:w-48\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.ModuleSelect, {\n                                                moduleColor: \"admin\",\n                                                value: moduleFilter,\n                                                onChange: (e)=>handleModuleFilterChange(e.target.value),\n                                                placeholder: \"M\\xf3dulos\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Todos os m\\xf3dulos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"ADMIN\",\n                                                        children: \"Administra\\xe7\\xe3o\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"RH\",\n                                                        children: \"RH\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"FINANCIAL\",\n                                                        children: \"Financeiro\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"SCHEDULING\",\n                                                        children: \"Agendamento\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"BASIC\",\n                                                        children: \"B\\xe1sico\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 385,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 384,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full sm:w-48\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.ModuleSelect, {\n                                                moduleColor: \"admin\",\n                                                value: companyFilter,\n                                                onChange: (e)=>handleCompanyFilterChange(e.target.value),\n                                                placeholder: \"Empresa\",\n                                                disabled: isLoadingCompanies,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Todas as empresas\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: company.id,\n                                                            children: company.name\n                                                        }, company.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 25\n                                                        }, void 0))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 403,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 402,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_16__.FilterButton, {\n                                            type: \"submit\",\n                                            moduleColor: \"admin\",\n                                            variant: \"primary\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"sm:hidden\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 19\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden sm:inline\",\n                                                    children: \"Filtrar\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 19\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 420,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_16__.FilterButton, {\n                                            type: \"button\",\n                                            onClick: handleResetFilters,\n                                            moduleColor: \"admin\",\n                                            variant: \"secondary\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"sm:hidden\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 19\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden sm:inline\",\n                                                    children: \"Limpar\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 19\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 425,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 370,\n                                    columnNumber: 15\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                            lineNumber: 358,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.MultiSelect, {\n                                label: \"Filtrar por Usu\\xe1rios\",\n                                value: usersFilter,\n                                onChange: handleUsersFilterChange,\n                                options: userOptions,\n                                placeholder: \"Selecione um ou mais usu\\xe1rios pelo nome...\",\n                                loading: isLoadingUserOptions,\n                                moduleOverride: \"admin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 439,\n                                columnNumber: 15\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                            lineNumber: 438,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                    lineNumber: 353,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 345,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.ModuleTable, {\n                moduleColor: \"admin\",\n                columns: [\n                    {\n                        header: 'Usuário',\n                        field: 'fullName',\n                        width: '20%'\n                    },\n                    {\n                        header: 'Email',\n                        field: 'email',\n                        width: '15%'\n                    },\n                    {\n                        header: 'Profissão',\n                        field: 'profession',\n                        width: '15%'\n                    },\n                    {\n                        header: 'Módulos',\n                        field: 'modules',\n                        width: '10%'\n                    },\n                    {\n                        header: 'Função',\n                        field: 'role',\n                        width: '10%'\n                    },\n                    {\n                        header: 'Status',\n                        field: 'active',\n                        width: '8%'\n                    },\n                    {\n                        header: 'Ações',\n                        field: 'actions',\n                        className: 'text-right',\n                        width: '14%',\n                        sortable: false\n                    }\n                ],\n                data: users,\n                isLoading: isLoading,\n                emptyMessage: \"Nenhum usu\\xe1rio encontrado\",\n                emptyIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                    lineNumber: 468,\n                    columnNumber: 20\n                }, void 0),\n                currentPage: currentPage,\n                totalPages: totalPages,\n                totalItems: totalUsers,\n                onPageChange: handlePageChange,\n                showPagination: true,\n                tableId: \"admin-users-table\",\n                enableColumnToggle: true,\n                defaultSortField: \"fullName\",\n                defaultSortDirection: \"asc\",\n                onSort: (field, direction)=>{\n                    // Quando a ordenação mudar, recarregar os usuários com os novos parâmetros de ordenação\n                    loadUsers(currentPage, search, statusFilter, moduleFilter, companyFilter, usersFilter, field, direction);\n                },\n                renderRow: (user, index, moduleColors, visibleColumns)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                        className: moduleColors.hoverBg,\n                        children: [\n                            visibleColumns.includes('fullName') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-10 w-10 rounded-full bg-neutral-200 dark:bg-gray-600 flex items-center justify-center text-neutral-600 dark:text-neutral-300 font-medium flex-shrink-0 overflow-hidden\",\n                                            children: user.profileImageFullUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: user.profileImageFullUrl,\n                                                alt: user.fullName,\n                                                className: \"w-full h-full object-cover\",\n                                                onError: (e)=>{\n                                                    e.target.onerror = null;\n                                                    e.target.style.display = 'none';\n                                                    e.target.parentNode.innerHTML = user.fullName.charAt(0).toUpperCase();\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 498,\n                                                columnNumber: 23\n                                            }, void 0) : user.fullName.charAt(0).toUpperCase()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 496,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-neutral-800 dark:text-neutral-100 truncate\",\n                                                    children: user.fullName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 513,\n                                                    columnNumber: 21\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-neutral-500 dark:text-neutral-400 truncate\",\n                                                    children: user.login\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 512,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 495,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 494,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('email') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300 truncate\",\n                                children: user.email\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 525,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('profession') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300\",\n                                children: user.professionObj ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            size: 14,\n                                            className: \"text-neutral-500 dark:text-neutral-400 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 534,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate\",\n                                            children: [\n                                                user.professionObj.name,\n                                                user.professionObj.group && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-neutral-400 dark:text-neutral-500 ml-1\",\n                                                    children: [\n                                                        \"(\",\n                                                        user.professionObj.group.name,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 535,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 533,\n                                    columnNumber: 19\n                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                    children: \"Sem profiss\\xe3o\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 545,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 531,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('modules') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-1\",\n                                    children: [\n                                        user.modules.slice(0, 2).map((module)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 text-xs rounded-full bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-neutral-300\",\n                                                children: MODULE_LABELS[module]\n                                            }, module, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 554,\n                                                columnNumber: 21\n                                            }, void 0)),\n                                        user.modules.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 text-xs rounded-full bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-neutral-300\",\n                                            children: [\n                                                \"+\",\n                                                user.modules.length - 2\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 562,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 552,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 551,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('role') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 text-xs rounded-full inline-flex items-center gap-1 \".concat(user.role === \"SYSTEM_ADMIN\" ? \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\" : user.role === \"COMPANY_ADMIN\" ? \"bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400\" : \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            size: 12,\n                                            className: \"flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 581,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate\",\n                                            children: user.role === \"SYSTEM_ADMIN\" ? \"Admin Sistema\" : user.role === \"COMPANY_ADMIN\" ? \"Admin Empresa\" : \"Funcionário\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 582,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 572,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 571,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('active') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit \".concat(user.active ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\" : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\"),\n                                    children: user.active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                size: 12,\n                                                className: \"flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 604,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Ativo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 605,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                size: 12,\n                                                className: \"flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 609,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Inativo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 610,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 595,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 594,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4 text-right text-sm font-medium\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_6__.Protected, {\n                                            permission: \"admin.users.edit\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleEditUser(user),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\",\n                                                id: \"edicaoUsuario\",\n                                                title: \"Editar\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 627,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 621,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 620,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_6__.Protected, {\n                                            permission: \"admin.permissions.manage\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleEditModules(user),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors\",\n                                                id: \"gerenciarModulo\",\n                                                title: \"Gerenciar m\\xf3dulos\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 632,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 631,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_6__.Protected, {\n                                            permission: \"admin.permissions.manage\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleManagePermissions(user),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-purple-500 dark:hover:text-purple-400 transition-colors\",\n                                                id: \"gerenciarPermissoes\",\n                                                title: \"Gerenciar permiss\\xf5es\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 649,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 643,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 642,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        (user.role !== \"SYSTEM_ADMIN\" || isSystemAdmin) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_6__.Protected, {\n                                            permission: \"admin.users.edit\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleManageRole(user),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-indigo-500 dark:hover:text-indigo-400 transition-colors\",\n                                                id: \"gerenciarFuncao\",\n                                                title: \"Alterar fun\\xe7\\xe3o\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 656,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 655,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        (user.role !== \"SYSTEM_ADMIN\" || isSystemAdmin) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_6__.Protected, {\n                                            permission: \"admin.users.edit\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleToggleStatus(user),\n                                                className: \"p-1 transition-colors \".concat(user.active ? \"text-neutral-500 dark:text-neutral-400 hover:text-amber-500 dark:hover:text-amber-400\" : \"text-neutral-500 dark:text-neutral-400 hover:text-green-500 dark:hover:text-green-400\"),\n                                                id: \"desativarUsuario\",\n                                                title: user.active ? \"Desativar\" : \"Ativar\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 680,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 670,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 669,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        (user.role !== \"SYSTEM_ADMIN\" || isSystemAdmin) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_6__.Protected, {\n                                            permission: \"admin.users.delete\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDeleteUser(user),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors\",\n                                                id: \"excluirUsuario\",\n                                                title: \"Excluir\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 688,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 687,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 619,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 618,\n                                columnNumber: 15\n                            }, void 0)\n                        ]\n                    }, user.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                        lineNumber: 492,\n                        columnNumber: 11\n                    }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 454,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_UserFormModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: userFormOpen,\n                onClose: ()=>setUserFormOpen(false),\n                user: selectedUser,\n                onSuccess: ()=>{\n                    setUserFormOpen(false);\n                    loadUsers();\n                },\n                currentUser: currentUser\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 706,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_ModulesModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: modulesModalOpen,\n                onClose: ()=>setModulesModalOpen(false),\n                user: selectedUser,\n                onSuccess: ()=>{\n                    setModulesModalOpen(false);\n                    loadUsers();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 717,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: confirmationDialogOpen,\n                onClose: ()=>setConfirmationDialogOpen(false),\n                onConfirm: confirmAction,\n                title: \"Confirmar a\\xe7\\xe3o\",\n                message: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.message) || \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 727,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_PermissionsModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: permissionsModalOpen,\n                onClose: ()=>setPermissionsModalOpen(false),\n                user: selectedUser,\n                onSuccess: ()=>{\n                    setPermissionsModalOpen(false);\n                    loadUsers();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 735,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_RoleModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: roleModalOpen,\n                onClose: ()=>setRoleModalOpen(false),\n                user: selectedUser,\n                onSuccess: ()=>{\n                    setRoleModalOpen(false);\n                    loadUsers();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 744,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n        lineNumber: 311,\n        columnNumber: 5\n    }, undefined);\n};\n_s(UsersPage, \"d8X6bUO97cMyw194FlS92cwYTas=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__.usePermissions\n    ];\n});\n_c = UsersPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UsersPage);\nvar _c;\n$RefreshReg$(_c, \"UsersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/admin/users/UsersPage.js\n"));

/***/ })

});