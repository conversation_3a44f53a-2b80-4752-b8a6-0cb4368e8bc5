// middleware-test.js
require('dotenv').config();
const cacheService = require('../../src/services/cacheService');
const AdvancedCacheMiddleware = require('../../src/middlewares/advancedCache');
const { cacheMiddleware, clearCacheMiddleware } = require('../../src/middlewares/cache');

// Test utilities
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logHeader(message) {
  log(`\n${colors.bold}=== ${message} ===${colors.reset}`, 'blue');
}

// Create jest mock
global.jest = {
  fn: () => {
    const mockFn = (...args) => {
      mockFn.mock.calls.push(args);
      return mockFn.mock.returnValue;
    };
    mockFn.mock = { 
      calls: [],
      returnValue: undefined
    };
    return mockFn;
  }
};

// Mock Express objects for middleware testing
function createMockExpressObjects(options = {}) {
  const req = {
    method: 'GET',
    originalUrl: '/api/test',
    query: {},
    user: {
      id: 'test-user-123',
      companyId: 'test-company-456',
      role: 'admin'
    },
    ...options.req
  };

  const res = {
    statusCode: 200,
    json: jest.fn(),
    send: jest.fn(),
    set: jest.fn(),
    ...options.res
  };

  const next = jest.fn();

  return { req, res, next };
}

class MiddlewareTestSuite {
  constructor() {
    this.testResults = {
      passed: 0,
      failed: 0,
      total: 0
    };
  }

  async runTest(testName, testFn) {
    this.testResults.total++;
    try {
      logInfo(`Executando: ${testName}`);
      await testFn();
      this.testResults.passed++;
      logSuccess(`${testName} - PASSOU`);
    } catch (error) {
      this.testResults.failed++;
      logError(`${testName} - FALHOU: ${error.message}`);
      console.error(error.stack);
    }
  }

  printResults() {
    logHeader('RESULTADOS DOS TESTES DE MIDDLEWARE');
    log(`Total de testes: ${this.testResults.total}`, 'blue');
    log(`Passou: ${this.testResults.passed}`, 'green');
    log(`Falhou: ${this.testResults.failed}`, this.testResults.failed > 0 ? 'red' : 'green');
    
    const successRate = (this.testResults.passed / this.testResults.total * 100).toFixed(1);
    log(`Taxa de sucesso: ${successRate}%`, successRate === '100.0' ? 'green' : 'yellow');
  }

  // Test 1: Basic Cache Middleware
  async testBasicCacheMiddleware() {
    await cacheService.clear('test:*');
    
    const middleware = cacheMiddleware('test', 60);
    const { req, res, next } = createMockExpressObjects();
    
    // Mock response data
    const responseData = { message: 'Test response', timestamp: new Date().toISOString() };
    
    // First call - should be cache miss
    await middleware(req, res, next);
    
    if (!next.mock.calls.length) {
      throw new Error('Next não foi chamado no cache miss');
    }
    
    // Simulate response
    res.json(responseData);
    
    // Wait a bit for async cache storage
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Second call - should be cache hit
    const { req: req2, res: res2, next: next2 } = createMockExpressObjects();
    await middleware(req2, res2, next2);
    
    // Should not call next on cache hit
    if (next2.mock.calls.length > 0) {
      throw new Error('Next foi chamado no cache hit');
    }
    
    if (!res2.json.mock.calls.length) {
      throw new Error('Response não foi enviada no cache hit');
    }
  }

  // Test 2: Advanced Cache Middleware - Smart Cache
  async testAdvancedSmartCache() {
    await cacheService.clear('test:smart:*');
    
    const middleware = AdvancedCacheMiddleware.smartCache({
      prefix: 'test:smart',
      ttl: 60,
      strategy: 'standard'
    });
    
    const { req, res, next } = createMockExpressObjects();
    const responseData = { data: 'Smart cache test', id: 123 };
    
    // First call - cache miss
    await middleware(req, res, next);
    
    if (!next.mock.calls.length) {
      throw new Error('Next não foi chamado no smart cache miss');
    }
    
    // Simulate successful response
    res.statusCode = 200;
    res.json(responseData);
    
    // Wait for async cache storage
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Verify cache headers were set
    if (!res.set.mock.calls.some(call => call[0]['X-Cache'] === 'MISS')) {
      throw new Error('Headers de cache não foram definidos corretamente');
    }
  }

  // Test 3: Session Cache Strategy
  async testSessionCacheStrategy() {
    await cacheService.clear('test:session:*');
    
    const middleware = AdvancedCacheMiddleware.sessionCache('test:session', 60);
    const { req, res, next } = createMockExpressObjects();
    
    await middleware(req, res, next);
    
    if (!next.mock.calls.length) {
      throw new Error('Next não foi chamado no session cache');
    }
    
    // Verify that user-specific key generation works
    const expectedKeyPattern = `test:session:user:${req.user.id}:company:${req.user.companyId}`;
    logInfo(`Padrão de chave esperado: ${expectedKeyPattern}`);
  }

  // Test 4: Search Cache Strategy
  async testSearchCacheStrategy() {
    await cacheService.clear('test:search:*');
    
    const middleware = AdvancedCacheMiddleware.searchCache('test:search', 60);
    
    // Test with search parameters
    const { req: reqWithSearch, res: resWithSearch, next: nextWithSearch } = createMockExpressObjects({
      req: { query: { search: 'test query', filter: 'active' } }
    });
    
    await middleware(reqWithSearch, resWithSearch, nextWithSearch);
    
    if (!nextWithSearch.mock.calls.length) {
      throw new Error('Next não foi chamado com parâmetros de busca');
    }
    
    // Test without search parameters (should skip cache)
    const { req: reqNoSearch, res: resNoSearch, next: nextNoSearch } = createMockExpressObjects({
      req: { query: {} }
    });
    
    await middleware(reqNoSearch, resNoSearch, nextNoSearch);
    
    if (!nextNoSearch.mock.calls.length) {
      throw new Error('Next não foi chamado sem parâmetros de busca');
    }
  }

  // Test 5: Reference Cache Strategy
  async testReferenceCacheStrategy() {
    await cacheService.clear('test:reference:*');
    
    const middleware = AdvancedCacheMiddleware.referenceCache('test:reference', 3600);
    const { req, res, next } = createMockExpressObjects();
    
    await middleware(req, res, next);
    
    if (!next.mock.calls.length) {
      throw new Error('Next não foi chamado no reference cache');
    }
    
    logInfo('Reference cache middleware executado com sucesso');
  }

  // Test 6: Clear Cache Middleware
  async testClearCacheMiddleware() {
    // First, set some cache data
    await cacheService.set('test:clear:1', { data: 'test1' }, 60);
    await cacheService.set('test:clear:2', { data: 'test2' }, 60);
    
    // Verify data exists
    const data1 = await cacheService.get('test:clear:1');
    const data2 = await cacheService.get('test:clear:2');
    
    if (!data1 || !data2) {
      throw new Error('Dados de teste não foram criados corretamente');
    }
    
    const middleware = clearCacheMiddleware('test:clear:*');
    const { req, res, next } = createMockExpressObjects({
      req: { method: 'POST' }
    });
    
    await middleware(req, res, next);
    
    if (!next.mock.calls.length) {
      throw new Error('Next não foi chamado no clear cache middleware');
    }
    
    // Simulate successful response
    res.statusCode = 200;
    res.json({ success: true });
    
    // Wait for async cache clearing
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Verify cache was cleared
    const clearedData1 = await cacheService.get('test:clear:1');
    const clearedData2 = await cacheService.get('test:clear:2');
    
    if (clearedData1 !== null || clearedData2 !== null) {
      throw new Error('Cache não foi limpo corretamente');
    }
  }

  // Test 7: Cache Statistics
  async testCacheStatistics() {
    const stats = await cacheService.getStats();
    
    if (!stats) {
      throw new Error('Estatísticas do cache não foram obtidas');
    }
    
    if (!stats.connected) {
      throw new Error('Cache não está conectado');
    }
    
    logInfo('Estatísticas do cache obtidas com sucesso');
    logInfo(`Memória usada: ${stats.memory?.used_memory_human || 'N/A'}`);
    logInfo(`Conexões: ${stats.stats?.total_connections_received || 'N/A'}`);
  }
}

// Main test execution
async function runMiddlewareTests() {
  logHeader('INICIANDO TESTES DE MIDDLEWARE DE CACHE');
  
  const testSuite = new MiddlewareTestSuite();

  try {
    // Initialize cache service
    logInfo('Inicializando serviço de cache...');
    const initResult = await cacheService.initialize();
    if (!initResult.success) {
      throw new Error(`Falha ao inicializar cache: ${initResult.error}`);
    }
    logSuccess('Serviço de cache inicializado com sucesso');

    // Run all middleware tests
    await testSuite.runTest('Middleware de Cache Básico', () => testSuite.testBasicCacheMiddleware());
    await testSuite.runTest('Smart Cache Avançado', () => testSuite.testAdvancedSmartCache());
    await testSuite.runTest('Estratégia de Session Cache', () => testSuite.testSessionCacheStrategy());
    await testSuite.runTest('Estratégia de Search Cache', () => testSuite.testSearchCacheStrategy());
    await testSuite.runTest('Estratégia de Reference Cache', () => testSuite.testReferenceCacheStrategy());
    await testSuite.runTest('Middleware de Limpeza de Cache', () => testSuite.testClearCacheMiddleware());
    await testSuite.runTest('Estatísticas de Cache', () => testSuite.testCacheStatistics());

  } catch (error) {
    logError(`Erro durante a inicialização: ${error.message}`);
  } finally {
    // Print results
    testSuite.printResults();
    
    // Close connection
    try {
      await cacheService.close();
      logInfo('Conexão com cache fechada');
    } catch (error) {
      logInfo(`Erro ao fechar conexão: ${error.message}`);
    }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runMiddlewareTests().catch(console.error);
}

module.exports = { MiddlewareTestSuite, runMiddlewareTests };
