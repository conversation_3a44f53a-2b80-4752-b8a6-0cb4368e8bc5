// quick-test.js
require('dotenv').config();
const cacheService = require('../../src/services/cacheService');

// Simple colors for output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function quickCacheTest() {
  log('\n=== TESTE RÁPIDO DO SISTEMA DE CACHE ===\n', 'bold');
  
  try {
    // Test 1: Connection
    log('1. Testando conexão com Redis...', 'blue');
    const initResult = await cacheService.initialize();
    
    if (initResult.success) {
      log('   ✅ Conexão estabelecida com sucesso!', 'green');
    } else {
      log(`   ❌ Falha na conexão: ${initResult.error}`, 'red');
      return;
    }
    
    // Test 2: Basic operations
    log('\n2. Testando operações básicas...', 'blue');
    
    const testKey = 'quick:test';
    const testValue = {
      message: 'Teste rápido do cache',
      timestamp: new Date().toISOString(),
      data: [1, 2, 3, 4, 5]
    };
    
    // SET
    log('   - Armazenando dados...', 'yellow');
    const setResult = await cacheService.set(testKey, testValue, 60);
    if (setResult) {
      log('   ✅ Dados armazenados com sucesso!', 'green');
    } else {
      log('   ❌ Falha ao armazenar dados', 'red');
      return;
    }
    
    // GET
    log('   - Recuperando dados...', 'yellow');
    const getValue = await cacheService.get(testKey);
    if (getValue && getValue.message === testValue.message) {
      log('   ✅ Dados recuperados corretamente!', 'green');
    } else {
      log('   ❌ Falha ao recuperar dados ou dados incorretos', 'red');
      return;
    }
    
    // TTL
    log('   - Verificando TTL...', 'yellow');
    const ttl = await cacheService.getTTL(testKey);
    if (ttl > 0 && ttl <= 60) {
      log(`   ✅ TTL correto: ${ttl} segundos`, 'green');
    } else {
      log(`   ⚠️  TTL inesperado: ${ttl} segundos`, 'yellow');
    }
    
    // DELETE
    log('   - Deletando dados...', 'yellow');
    const deleteResult = await cacheService.delete(testKey);
    if (deleteResult) {
      log('   ✅ Dados deletados com sucesso!', 'green');
    } else {
      log('   ❌ Falha ao deletar dados', 'red');
    }
    
    // Test 3: Performance check
    log('\n3. Teste rápido de performance...', 'blue');
    
    const perfKey = 'quick:perf';
    const perfData = { data: Array.from({length: 1000}, (_, i) => `item-${i}`) };
    
    const start = Date.now();
    await cacheService.set(perfKey, perfData, 60);
    await cacheService.get(perfKey);
    const duration = Date.now() - start;
    
    if (duration < 100) {
      log(`   ✅ Performance boa: ${duration}ms`, 'green');
    } else if (duration < 500) {
      log(`   ⚠️  Performance aceitável: ${duration}ms`, 'yellow');
    } else {
      log(`   ❌ Performance ruim: ${duration}ms`, 'red');
    }
    
    // Clean up
    await cacheService.delete(perfKey);
    
    // Test 4: Statistics
    log('\n4. Verificando estatísticas...', 'blue');
    const stats = await cacheService.getStats();
    if (stats) {
      log('   ✅ Estatísticas obtidas com sucesso!', 'green');
      log(`   - Memória usada: ${stats.memory?.used_memory_human || 'N/A'}`, 'blue');
      log(`   - Conexões: ${stats.stats?.total_connections_received || 'N/A'}`, 'blue');
      log(`   - Comandos processados: ${stats.stats?.total_commands_processed || 'N/A'}`, 'blue');
    } else {
      log('   ⚠️  Não foi possível obter estatísticas', 'yellow');
    }
    
    // Final result
    log('\n=== RESULTADO FINAL ===', 'bold');
    log('🎉 SISTEMA DE CACHE FUNCIONANDO CORRETAMENTE!', 'green');
    log('\nO seu sistema de cache Redis está operacional e pronto para uso.', 'blue');
    
  } catch (error) {
    log('\n=== ERRO ===', 'bold');
    log(`❌ Erro durante o teste: ${error.message}`, 'red');
    console.error(error.stack);
  } finally {
    // Close connection
    try {
      await cacheService.close();
      log('\n✅ Conexão fechada com sucesso.', 'green');
    } catch (error) {
      log(`⚠️  Erro ao fechar conexão: ${error.message}`, 'yellow');
    }
  }
}

// Run quick test if this file is executed directly
if (require.main === module) {
  quickCacheTest().catch(console.error);
}

module.exports = { quickCacheTest };
